import natural from "natural";
const { Porter<PERSON>temmer, WordTokenizer } = natural;
const tokenizer = new WordTokenizer();

// Predefined stop words
const STOP_WORDS = new Set([
  "a",
  "an",
  "the",
  "and",
  "or",
  "but",
  "of",
  "to",
  "in",
  "on",
  "at",
  "for",
  "with",
  "by",
  "as",
  "into",
  "through",
  "during",
  "before",
  "after",
  "above",
  "below",
  "from",
  "up",
  "down",
  "out",
  "off",
  "over",
  "under",
  "again",
  "further",
  "then",
  "once",
  "here",
  "there",
  "when",
  "where",
  "why",
  "how",
  "all",
  "any",
  "both",
  "each",
  "few",
  "more",
  "most",
  "other",
  "some",
  "such",
  "no",
  "nor",
  "not",
  "only",
  "own",
  "same",
  "so",
  "than",
  "too",
  "very",
]);

export function normalizeText(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, "") // Remove special chars
    .replace(/\s+/g, " ") // Collapse whitespace
    .trim();
}

export function tokenizeText(text: string): string[] {
  const normalized = normalizeText(text);
  const tokens = tokenizer.tokenize(normalized) || [];
  return tokens.filter((token) => !STOP_WORDS.has(token));
}

export function stemTokens(tokens: string[]): string[] {
  return tokens.map((token) => PorterStemmer.stem(token));
}

export function generateNGrams(
  text: string,
  sizes: number[] = [2, 3]
): string[] {
  const tokens = normalizeText(text).split(" ");
  const ngrams = new Set<string>();

  for (const size of sizes) {
    for (let i = 0; i <= tokens.length - size; i++) {
      const ngram = tokens.slice(i, i + size).join(" ");
      ngrams.add(ngram);
    }
  }

  return Array.from(ngrams);
}
