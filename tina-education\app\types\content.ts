export interface ContentBase {
  id: string;
  type: "book" | "journal" | "article";
  title: string;
  authors: string[];
  publicationDate: string;
  keywords?: string[];
}

export interface Book extends ContentBase {
  type: "book";
  isbn?: string;
  publisher: string;
  pages?: number;
}

export interface Journal extends ContentBase {
  type: "journal";
  volume: string;
  issue: string;
  issn?: string;
}

export interface Article extends ContentBase {
  type: "article";
  journalId?: string;
  doi?: string;
  abstract: string;
}

export type ContentItem = Book | Journal | Article;
export type ContentIndex = Record<string, ContentItem>;

export type ProcessedDocument = ContentItem & {
  searchableText: string;
  tokens: string[];
  stems: string[];
  ngrams: string[];
  boost: number;
  normalizedType: string;
};