import { prisma } from "../../../prisma";
import {
  <PERSON>a<PERSON><PERSON><PERSON>,
  FaBookOpen,
  FaFile,
  Fa<PERSON><PERSON>,
  Fa<PERSON><PERSON><PERSON><PERSON>ist,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>a<PERSON><PERSON><PERSON><PERSON>,
} from "react-icons/fa";

async function getAdminStats() {
  try {
    const [
      totalUsers,
      totalReviewers,
      totalManuscripts,
      pendingReviews,
      pendingApplications,
      manuscriptsThisMonth,
      reviewsThisMonth,
    ] = await Promise.all([
      // Total users
      prisma.user.count(),

      // Total reviewers
      prisma.user.count({
        where: { role: "REVIEWER" },
      }),

      // Total manuscripts
      prisma.manuscript.count(),

      // Pending reviews
      prisma.review.count({
        where: { status: "PENDING" },
      }),

      // Pending reviewer applications
      prisma.reviewerApplication.count({
        where: { status: "PENDING" },
      }),

      // Manuscripts submitted this month
      prisma.manuscript.count({
        where: {
          createdAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
          },
        },
      }),

      // Reviews completed this month
      prisma.review.count({
        where: {
          status: "REVIEW_SUBMITTED",
          updatedAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
          },
        },
      }),
    ]);

    return {
      totalUsers,
      totalReviewers,
      totalManuscripts,
      pendingReviews,
      pendingApplications,
      manuscriptsThisMonth,
      reviewsThisMonth,
    };
  } catch (error) {
    console.error("Failed to fetch admin stats:", error);
    return {
      totalUsers: 0,
      totalReviewers: 0,
      totalManuscripts: 0,
      pendingReviews: 0,
      pendingApplications: 0,
      manuscriptsThisMonth: 0,
      reviewsThisMonth: 0,
    };
  }
}

export default async function AdminStats() {
  const stats = await getAdminStats();

  const statCards = [
    {
      title: "Total Users",
      value: stats.totalUsers,
      icon: <FaUsers />,
      color: "bg-blue-50 text-blue-700",
      change: `+${stats.totalUsers > 0 ? Math.round((stats.totalUsers / 100) * 5) : 0} this month`,
    },
    {
      title: "Active Reviewers",
      value: stats.totalReviewers,
      icon: <FaBookOpen />,
      color: "bg-green-50 text-green-700",
      change: `${Math.round((stats.totalReviewers / Math.max(stats.totalUsers, 1)) * 100)}% of users`,
    },
    {
      title: "Total Manuscripts",
      value: stats.totalManuscripts,
      icon: <FaFile />,
      color: "bg-purple-50 text-purple-700",
      change: `+${stats.manuscriptsThisMonth} this month`,
    },
    {
      title: "Pending Reviews",
      value: stats.pendingReviews,
      icon: <FaClock />,
      color:
        stats.pendingReviews > 0
          ? "bg-yellow-50 text-yellow-700"
          : "bg-gray-50 text-gray-700",
      change: `${stats.reviewsThisMonth} completed this month`,
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statCards.map((stat, index) => (
        <div
          key={index}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">{stat.title}</p>
              <p className="text-3xl font-bold text-gray-900 mt-1">
                {stat.value.toLocaleString()}
              </p>
              <p className="text-xs text-gray-500 mt-1">{stat.change}</p>
            </div>
            <div className={`p-3 rounded-full ${stat.color}`}>
              <span className="text-2xl">{stat.icon}</span>
            </div>
          </div>
        </div>
      ))}

      {/* Additional Stats Row */}
      <div className="md:col-span-2 lg:col-span-4 grid grid-cols-1 md:grid-cols-3 gap-6 mt-4">
        {/* Pending Applications Alert */}
        {stats.pendingApplications > 0 && (
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-center">
              <span className="text-2xl mr-3">
                <FaClipboardList />
              </span>
              <div>
                <h3 className="font-medium text-orange-900">
                  {stats.pendingApplications} Pending Application
                  {stats.pendingApplications !== 1 ? "s" : ""}
                </h3>
                <p className="text-sm text-orange-700">
                  Reviewer applications awaiting review
                </p>
              </div>
            </div>
          </div>
        )}

        {/* System Health */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <span className="text-2xl mr-3">
              <FaCheck />
            </span>
            <div>
              <h3 className="font-medium text-green-900">System Healthy</h3>
              <p className="text-sm text-green-700">All services operational</p>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <span className="text-2xl mr-3">
              <FaChartBar />
            </span>
            <div>
              <h3 className="font-medium text-gray-900">Activity This Month</h3>
              <p className="text-sm text-gray-700">
                {stats.manuscriptsThisMonth} submissions,{" "}
                {stats.reviewsThisMonth} reviews
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
