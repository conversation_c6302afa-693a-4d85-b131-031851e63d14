import React from "react";

interface SortBarProps {
  displayInfo: string;
  showSort?: boolean;
  sortValue?: string;
  onSortChange?: (value: string) => void;
  pageInfo?: React.ReactNode;
}

const SortBar: React.FC<SortBarProps> = ({
  displayInfo,
  showSort = false,
  sortValue = "relevance",
  onSortChange,
  pageInfo,
}) => (
  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 py-2">
    <div className="text-sm text-gray-700">{displayInfo}</div>
    <div className="flex items-center gap-4">
      {showSort && (
        <select
          className="border text-gray-600 border-gray-400 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-300"
          value={sortValue}
          onChange={(e) => onSortChange && onSortChange(e.target.value)}
        >
          <option value="relevance">Relevance</option>
          <option value="price-low">Price: Low to High</option>
          <option value="price-high">Price: High to Low</option>
          <option value="title">Title</option>
        </select>
      )}
      {pageInfo}
    </div>
  </div>
);

export default SortBar;
