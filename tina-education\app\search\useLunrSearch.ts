import lunr from "lunr";
import { useEffect, useRef, useState } from "react";
import { documents, DocumentType } from "./sampleData";
import { SearchFilters } from "../types/search";

interface LunrSearchResult {
  results: DocumentType[];
  total: number;
  page: number;
  totalPages: number;
}

export function useLunrSearch() {
  const [query, setQuery] = useState("");
  const [filters, setFilters] = useState<SearchFilters>({});
  const [results, setResults] = useState<LunrSearchResult>({ results: [], total: 0, page: 1, totalPages: 1 });
  const [isSearching, setIsSearching] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const indexRef = useRef<lunr.Index | null>(null);

  // Build the Lunr index once
  useEffect(() => {
    indexRef.current = lunr(function () {
      this.ref("id");
      this.field("title");
      this.field("authors");
      this.field("type");
      this.field("category");
      this.field("series");
      documents.forEach((doc) => this.add(doc));
    });
  }, []);

  // Perform search
  useEffect(() => {
    if (!indexRef.current) return;
    setIsSearching(true);
    setLoading(true);
    try {
      let lunrResults = query
        ? indexRef.current.search(query)
        : documents.map((doc) => ({ ref: doc.id }));
      // Map refs to docs
      let matched = lunrResults
        .map((r: any) => documents.find((d) => d.id === r.ref))
        .filter(Boolean) as DocumentType[];
      // Apply filters (example: by author/type)
      if (filters.authors) {
        matched = matched.filter((doc) =>
          doc.authors && doc.authors.includes(filters.authors[0])
        );
      }
      if (filters.types) {
        matched = matched.filter((doc) =>
          doc.type && filters.types.includes(doc.type)
        );
      }
      setResults({ results: matched, total: matched.length, page: 1, totalPages: 1 });
      setError(null);
    } catch (e: any) {
      setError(e.message);
      setResults({ results: [], total: 0, page: 1, totalPages: 1 });
    }
    setLoading(false);
    setIsSearching(false);
  }, [query, filters]);

  return {
    query,
    setQuery,
    filters,
    setFilters,
    results,
    isSearching,
    loading,
    error,
  };
}
