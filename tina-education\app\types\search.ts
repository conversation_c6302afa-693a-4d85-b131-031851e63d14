// types/search.ts
import { ProcessedDocument } from "./content";

export interface SearchIndex {
  documents: Record<string, ProcessedDocument>;
  tokenIndex: Record<string, Set<string>>;
  ngramIndex: Record<string, Set<string>>;
  metadataIndex: {
    types: Set<string>;
    authors: Set<string>;
    years: Set<number>;
  };
}

export interface SerializableSearchIndex {
  documents: Record<string, ProcessedDocument>;
  tokenIndex: Record<string, string[]>;
  ngramIndex: Record<string, string[]>;
  metadataIndex: {
    types: string[];
    authors: string[];
    years: number[];
  };
}

export interface SearchOptions {
  filters?: SearchFilters;
  page?: number;
  perPage?: number;
  boostRecent?: boolean;
}

export interface SearchFilters {
  types?: string[];
  authors?: string[];
  yearRange?: { min?: number; max?: number };
}

export interface ScoredDocument {
  document: ProcessedDocument;
  score: number;
}

export interface SearchResults {
  results: ScoredDocument[];
  total: number;
  page: number;
  perPage: number;
  totalPages: number;
}

export interface Suggestion {
  text: string;
  count: number;
}
