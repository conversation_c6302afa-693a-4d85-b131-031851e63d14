// This is a placeholder for your searchable data. Replace with your real data source or import.
export interface DocumentType {
  id: string;
  title: string;
  authors: string[];
  type: string;
  price: number;
  cover: string;
  publisher: string;
  category: string;
  series: string;
}

export const documents: DocumentType[] = [
  {
    id: '1',
    title: 'The Girl with the Dragon Tattoo',
    authors: ['<PERSON><PERSON><PERSON>'],
    type: 'Mystery',
    price: 10,
    cover: '',
    publisher: 'Tina Publishing',
    category: 'Mystery',
    series: 'Series 1',
  },
  {
    id: '2',
    title: 'The Woman in the Window',
    authors: ['<PERSON><PERSON><PERSON><PERSON> Finn'],
    type: 'Thriller',
    price: 12,
    cover: '',
    publisher: 'Other Imprint',
    category: 'Thriller',
    series: 'Series 2',
  },
  // ...add more sample documents
];
