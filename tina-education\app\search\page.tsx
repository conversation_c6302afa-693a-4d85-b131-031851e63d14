'use client';

import React, { useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { useLunrSearch } from "./useLunrSearch";
import Link from "next/link";
import SortBar from "../components/SortBar";
import FilterSidebar from "../components/FilterSidebar";
import ResultCard from "../components/ResultCard";
import { SearchFilters } from "../types/search";

const filterOptions = {
  Author: [
    "<PERSON>",
    "<PERSON>",
    "<PERSON><PERSON><PERSON>",
    "Liane Moriarty",
    "A.J. Finn",
  ],
  Category: ["Mystery", "Thriller", "Drama"],
  Imprint: ["Tina Publishing", "Other Imprint"],
  "Product Type": ["Paperback", "Hardback", "eBook", "Audiobook"],
  Series: ["Series 1", "Series 2"],
};

// Helper: Convert SearchFilters to sidebar filters
function filtersToSidebar(filters: SearchFilters): {
  [key: string]: string | null;
} {
  return {
    Author: filters.authors?.[0] || null,
    Category: filters.types?.[0] || null,
    // Add more mappings as needed
  };
}
// Helper: Convert sidebar filters to SearchFilters
function sidebarToFilters(sidebar: {
  [key: string]: string | null;
}): SearchFilters {
  return {
    authors: sidebar.Author ? [sidebar.Author] : undefined,
    types: sidebar.Category ? [sidebar.Category] : undefined,
    // Add more mappings as needed
  };
}
// Helper: Map ProcessedDocument to ResultCard shape
function mapToResultCard(doc: any): any {
  return {
    id: doc.id,
    title: doc.title,
    author: doc.authors?.[0] || "Unknown Author",
    price: doc.price || "-",
    type: doc.type,
    cover: doc.cover || "/images/book-placeholder.jpg",
    imprint: doc.publisher || doc.imprint || "",
    category: doc.category || doc.keywords?.[0] || doc.normalizedType || "",
    series: doc.series || "",
  };
}

const SearchPage = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const {
    query,
    setQuery,
    filters,
    setFilters,
    results,
    isSearching,
    loading,
    error,
  } = useLunrSearch();

  const [showFilters, setShowFilters] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [sort, setSort] = useState("relevance");
  const [page, setPage] = useState(1);
  const resultsPerPage = 10;
  const [sidebarFilters, setSidebarFilters] = useState(
    filtersToSidebar(filters)
  );

  // Initialize from URL on first load
  useEffect(() => {
    if (isInitialLoad && searchParams) {
      const urlQuery = searchParams.get("q") || "";
      const urlTypes = searchParams.get("types")?.split(",") || [];
      const minYear = searchParams.get("minYear");
      const maxYear = searchParams.get("maxYear");
      setQuery(urlQuery);
      setFilters({
        types: urlTypes,
        yearRange: {
          min: minYear ? parseInt(minYear) : undefined,
          max: maxYear ? parseInt(maxYear) : undefined,
        },
      });
      setIsInitialLoad(false);
    }
  }, [searchParams, isInitialLoad, setQuery, setFilters]);

  // Update page in search options
  useEffect(() => {
    // If your useSearch supports page/perPage, you can pass them here
    // Otherwise, filter results manually below
  }, [page]);

  // Pagination logic
  const totalResults = results?.total || 0;
  const pageCount =
    results?.totalPages || Math.ceil(totalResults / resultsPerPage);
  const currentPage = results?.page || page;
  const pageInfo = (
    <div className="flex items-center gap-2 text-gray-600">
      <button
        className="px-2 py-1 rounded border"
        disabled={currentPage === 1}
        onClick={() => setPage((p) => Math.max(1, p - 1))}
      >
        &lt;
      </button>
      <span className="text-sm">
        Page {currentPage} of {pageCount}
      </span>
      <button
        className="px-2 py-1 rounded border"
        disabled={currentPage === pageCount}
        onClick={() => setPage((p) => Math.min(pageCount, p + 1))}
      >
        &gt;
      </button>
    </div>
  );

  // Search input handler
  const handleSearchInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
    setPage(1);
  };

  // Keep sidebar filters in sync with search filters
  useEffect(() => {
    setSidebarFilters(filtersToSidebar(filters));
  }, [filters]);

  // When sidebar filters change, update search filters
  useEffect(() => {
    setFilters(sidebarToFilters(sidebarFilters));
    setPage(1);
  }, [sidebarFilters]);

  return (
    <div className="min-h-screen mt-19  bg-gray-50">
      {/* Breadcrumb Section */}
      <div className="px-4 md:px-10 py-4 border-b border-blue-500 bg-white">
        <nav className="text-s text-black">
          <Link href="/" className="hover:underline">
            Home
          </Link>
          <span className="mx-2">/</span>
          <span>Search Results</span>
        </nav>
      </div>

      {/* Recap Section */}
      <div className="px-4 md:px-10 py-6 bg-white border-b border-gray-200">
        <h1 className="text-2xl font-bold text-black mb-2">
          Search Results for "{query}"
        </h1>
        <p className="text-gray-700 text-sm">
          {isSearching ? "Searching..." : `${totalResults} results found`}
        </p>
        <input
          type="text"
          className="mt-4 text-gray-500 border rounded px-3 py-2 w-full max-w-md"
          placeholder="Search for books, articles, journals..."
          value={query}
          onChange={handleSearchInput}
        />
      </div>

      {/* Sort Bar */}
      <div className="px-4 md:px-10 mt-6">
        <SortBar
          displayInfo={`Displaying ${(currentPage - 1) * resultsPerPage + 1}-${Math.min(currentPage * resultsPerPage, totalResults)} of ${totalResults} results`}
          showSort={true}
          sortValue={sort}
          onSortChange={setSort}
          pageInfo={pageInfo}
        />
      </div>

      {/* Main Content */}
      <div className="px-4 md:px-10 flex flex-col md:flex-row gap-8 mt-4">
        {/* Filter Sidebar */}
        <FilterSidebar
          filterOptions={filterOptions}
          filters={sidebarFilters}
          setFilters={setSidebarFilters}
        />
        {/* Results */}
        <main className="w-full text-gray-600 md:w-3/4">
          {loading && <div>Loading Search Results</div>}
          {error && (
            <div className="text-red-500">Error loading Search Results</div>
          )}
          {isSearching && <div>Searching for Results</div>}
          {!isSearching && results && results.results.length === 0 && (
            <div>No results found.</div>
          )}
          {!isSearching &&
            results &&
            results.results.map((document: any) => (
              <ResultCard
                key={document.id}
                result={mapToResultCard(document)}
              />
            ))}
        </main>
      </div>

      {/* Bottom Sort Bar (no sort field) */}
      <div className="px-4 md:px-10 mt-8 pb-12">
        <SortBar
          displayInfo={`Displaying ${(currentPage - 1) * resultsPerPage + 1}-${Math.min(currentPage * resultsPerPage, totalResults)} of ${totalResults} results`}
          showSort={false}
          pageInfo={pageInfo}
        />
      </div>
    </div>
  );
};

export default SearchPage;
