import { useEffect, useState } from "react";
import { SearchIndex, SerializableSearchIndex } from "../types/search";

export function useSearchIndex() {
  const [index, setIndex] = useState<SearchIndex | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const loadIndex = async () => {
      try {
        const response = await fetch("/search-index.json");
        const data: SerializableSearchIndex = await response.json();

        // Convert arrays back to Sets (no 'this' needed)
        const processedIndex: SearchIndex = {
          documents: data.documents,
          tokenIndex: deserializeIndex(data.tokenIndex),
          ngramIndex: deserializeIndex(data.ngramIndex),
          metadataIndex: {
            types: new Set(data.metadataIndex.types),
            authors: new Set(data.metadataIndex.authors),
            years: new Set(data.metadataIndex.years),
          },
        };

        setIndex(processedIndex);
      } catch (err) {
        setError(
          err instanceof Error ? err : new Error("Failed to load search index")
        );
      } finally {
        setLoading(false);
      }
    };

    loadIndex();
  }, []);

  return { index, loading, error };
}

// Standalone function (no 'this' needed)
function deserializeIndex(
  index: Record<string, string[]>
): Record<string, Set<string>> {
  return Object.fromEntries(
    Object.entries(index).map(([key, value]) => [key, new Set(value)])
  );
}
