import { DataLoader } from "./data-loader.ts";
import { DocumentProcessor } from "./document-processor.ts";
import { SearchIndex, SerializableSearchIndex } from "../types/search.ts";
import fs from "fs/promises";

export class SearchService {
  private loader: DataLoader;
  private processor: DocumentProcessor;

  constructor(contentPath: string = "content") {
    this.loader = new DataLoader(contentPath);
    this.processor = new DocumentProcessor();
  }

  async buildSearchIndex(): Promise<SearchIndex> {
    // 1. Load raw content
    const rawContent = await this.loader.loadAllContent();

    // 2. Process documents
    const processedDocs = rawContent.map((doc) =>
      this.processor.processDocument(doc)
    );

    // 3. Build search index
    const index: SearchIndex = {
      documents: {},
      tokenIndex: {},
      ngramIndex: {},
      metadataIndex: {
        types: new Set(),
        authors: new Set(),
        years: new Set(),
      },
    };

    for (const doc of processedDocs) {
      // Add to documents store
      index.documents[doc.id] = doc;

      // Index by tokens (stemmed words)
      for (const token of doc.stems) {
        index.tokenIndex[token] = index.tokenIndex[token] || new Set();
        index.tokenIndex[token].add(doc.id);
      }

      // Index by ngrams
      for (const ngram of doc.ngrams) {
        index.ngramIndex[ngram] = index.ngramIndex[ngram] || new Set();
        index.ngramIndex[ngram].add(doc.id);
      }

      // Update metadata indexes
      index.metadataIndex.types.add(doc.type);
      doc.authors.forEach((author) => index.metadataIndex.authors.add(author));
      const year = new Date(doc.publicationDate).getFullYear();
      index.metadataIndex.years.add(year);
    }

    return index;
  }

  async saveIndexToFile(index: SearchIndex, filePath: string): Promise<void> {
    // Convert Sets to Arrays for JSON serialization
    const serializableIndex: SerializableSearchIndex = {
      documents: index.documents,
      tokenIndex: this.serializeIndex(index.tokenIndex),
      ngramIndex: this.serializeIndex(index.ngramIndex),
      metadataIndex: {
        types: Array.from(index.metadataIndex.types),
        authors: Array.from(index.metadataIndex.authors),
        years: Array.from(index.metadataIndex.years),
      },
    };

    await fs.writeFile(filePath, JSON.stringify(serializableIndex));
  }

  private serializeIndex(
    index: Record<string, Set<string>>
  ): Record<string, string[]> {
    return Object.fromEntries(
      Object.entries(index).map(([key, value]) => [key, Array.from(value)])
    );
  }

  async loadIndexFromFile(filePath: string): Promise<SearchIndex> {
    const data = await fs.readFile(filePath, "utf-8");
    const serialized: SerializableSearchIndex = JSON.parse(data);

    return {
      documents: serialized.documents,
      tokenIndex: this.deserializeIndex(serialized.tokenIndex),
      ngramIndex: this.deserializeIndex(serialized.ngramIndex),
      metadataIndex: {
        types: new Set(serialized.metadataIndex.types),
        authors: new Set(serialized.metadataIndex.authors),
        years: new Set(serialized.metadataIndex.years),
      },
    };
  }

  private deserializeIndex(
    index: Record<string, string[]>
  ): Record<string, Set<string>> {
    return Object.fromEntries(
      Object.entries(index).map(([key, value]) => [key, new Set(value)])
    );
  }
}
