body {
  background-color: #081f28;
  color: #708284;
  font-family: 'Helvetica Neue', Helvetica, sans-serif;
  font-size: 18px;
}

h1 {
  margin: 0;
  padding: 0;
  font-size: 4em;
}

a {
  color: #2076c7;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

h2 {
  font-weight: normal;
  font-size: 1.2em;
}

h3 {
  font-size: 2.6em;
  margin: 0;
}

h1, h2 {
  margin: 0;
}

header span {
  font-size: 0.5em;
  font-weight: normal;
}

nav ul {
  margin: 0;
  padding: 6px 0 0 3px;
}

nav li {
  list-style: none;
  display: inline;
  padding-right: 12px;
  font-weight: bold;
  font-size: 0.9em;
}

footer ul {
  list-style: none;
  padding: 12px 0;
}

footer ul li {
  float: left;
  margin-right: 40px;
  font-size: 0.8em;
  font-weight: bold;
}

.wrap {
  width: 960px;
  margin: 20px auto 0 auto;
}

header {
  border-top: 4px solid #708284;
  padding: 4px 0 12px 0;
}

article header {
  border-top: 2px solid #708284;
}

pre {
  border-radius: 2px;
  background-color: #03262f;
  padding: 6px 0;
  color: #819090;
}

p {
  line-height: 1.4em;
}

section {
  margin-top: 40px;
}

section.columns {
  -webkit-column-count: 2;
  -webkit-column-gap: 50px;
  -webkit-hyphens: auto;
  min-height: 720px;
  font-size: 18px;
}

.download ul {
  list-style: none;
  padding: 0;
}

.download li {
  font-size: 1.2em;
}

pre .keyword, pre .special {
  font-weight: bold;
  color: #2076c7
}

pre .string, pre .regexp {
  color: #728a06
}

pre .class { 
  color: #A57707;
}

pre .number {
  color: #D11c24
}

pre .comment {
  color: grey;
  font-style: italic;
}
