import lunr from "lunr";
import { useEffect, useRef, useState } from "react";
import { documents } from "./sampleData";

export function useLunrSearch() {
  const [query, setQuery] = useState("");
  const [filters, setFilters] = useState({});
  const [results, setResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const indexRef = useRef(null);

  // Build the Lunr index once
  useEffect(() => {
    indexRef.current = lunr(function () {
      this.ref("id");
      this.field("title");
      this.field("authors");
      this.field("type");
      this.field("category");
      this.field("series");
      documents.forEach((doc) => this.add(doc));
    });
  }, []);

  // Perform search
  useEffect(() => {
    if (!indexRef.current) return;
    setIsSearching(true);
    setLoading(true);
    try {
      let lunrResults = query
        ? indexRef.current.search(query)
        : documents.map((doc) => ({ ref: doc.id }));
      // Map refs to docs
      let matched = lunrResults
        .map((r) => documents.find((d) => d.id === r.ref))
        .filter(Boolean);
      // Apply filters (example: by author/type)
      if (filters.authors) {
        matched = matched.filter((doc) =>
          doc.authors && doc.authors.includes(filters.authors[0])
        );
      }
      if (filters.types) {
        matched = matched.filter((doc) =>
          doc.type && filters.types.includes(doc.type)
        );
      }
      setResults(matched);
      setError(null);
    } catch (e) {
      setError(e.message);
      setResults([]);
    }
    setLoading(false);
    setIsSearching(false);
  }, [query, filters]);

  return {
    query,
    setQuery,
    filters,
    setFilters,
    results: { results, total: results.length, page: 1, totalPages: 1 },
    isSearching,
    loading,
    error,
  };
}
