import React from "react";
import Link from "next/link";

interface ResultCardProps {
  result: {
    id: string;
    title: string;
    author: string;
    price: string;
    type: string;
    cover: string;
    imprint: string;
    category: string;
    series: string;
  };
}

const ResultCard: React.FC<ResultCardProps> = ({ result }) => (
  <div className="flex flex-col sm:flex-row bg-white rounded-lg border border-gray-200 p-4 mb-6 shadow-sm">
    <div className="w-full sm:w-32 h-44 flex-shrink-0 bg-gray-200 border border-gray-300 flex items-center justify-center mb-4 sm:mb-0 sm:mr-6">
      <img src={result.cover} alt={result.title} className="w-full h-full object-cover rounded" />
    </div>
    <div className="flex flex-col justify-between flex-grow">
      <div>
        <h2 className="text-xl font-bold text-black mb-2 line-clamp-2">{result.title}</h2>
        <p className="text-md text-gray-700 mb-1">By {result.author}</p>
        <p className="text-sm text-gray-600 mb-1">{result.category} | {result.type} | {result.imprint}</p>
        <p className="text-md text-blue-900 font-bold mb-2">{result.price}</p>
      </div>
      <div>
        <Link href={`/repository/${result.id}`} className="text-blue-900 hover:underline text-sm font-medium">View Details</Link>
      </div>
    </div>
  </div>
);

export default ResultCard;
