import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const genreData = [
  // Fiction
  {
    name: 'Fiction',
    slug: 'fiction',
    description: 'Imaginative literature including novels, short stories, and novellas',
    children: [
      {
        name: 'Crime, Thriller and Mystery',
        slug: 'crime-thriller-mystery',
        description: 'Suspenseful stories involving crime, mystery, and thriller elements'
      },
      {
        name: 'Romance',
        slug: 'romance',
        description: 'Stories focused on romantic relationships and love'
      },
      {
        name: 'Science Fiction',
        slug: 'science-fiction',
        description: 'Speculative fiction dealing with futuristic concepts and technology'
      },
      {
        name: 'Fantasy',
        slug: 'fantasy',
        description: 'Stories featuring magical or supernatural elements'
      },
      {
        name: 'Historical Fiction',
        slug: 'historical-fiction',
        description: 'Stories set in the past, recreating historical periods'
      },
      {
        name: 'Literary Fiction',
        slug: 'literary-fiction',
        description: 'Character-driven narratives with artistic and literary merit'
      },
      {
        name: 'Horror',
        slug: 'horror',
        description: 'Stories intended to frighten, unsettle, or create suspense'
      },
      {
        name: 'Adventure',
        slug: 'adventure',
        description: 'Action-packed stories with exciting journeys and quests'
      }
    ]
  },
  
  // Non-Fiction
  {
    name: 'Non-Fiction',
    slug: 'non-fiction',
    description: 'Factual books based on real events, people, and information',
    children: [
      {
        name: 'Biography & Autobiography',
        slug: 'biography-autobiography',
        description: 'Life stories of real people'
      },
      {
        name: 'History',
        slug: 'history',
        description: 'Books about past events and historical periods'
      },
      {
        name: 'Science & Technology',
        slug: 'science-technology',
        description: 'Books about scientific discoveries and technological advances'
      },
      {
        name: 'Health & Wellness',
        slug: 'health-wellness',
        description: 'Books about physical and mental health, fitness, and well-being'
      },
      {
        name: 'Business & Economics',
        slug: 'business-economics',
        description: 'Books about business strategies, economics, and finance'
      },
      {
        name: 'Self-Help',
        slug: 'self-help',
        description: 'Books aimed at personal improvement and development'
      },
      {
        name: 'Travel',
        slug: 'travel',
        description: 'Books about places, cultures, and travel experiences'
      },
      {
        name: 'Politics & Social Issues',
        slug: 'politics-social-issues',
        description: 'Books about political systems and social topics'
      }
    ]
  },
  
  // Academic & Educational
  {
    name: 'Academic & Educational',
    slug: 'academic-educational',
    description: 'Scholarly works and educational materials',
    children: [
      {
        name: 'Textbooks',
        slug: 'textbooks',
        description: 'Educational books for academic courses'
      },
      {
        name: 'Research & Reference',
        slug: 'research-reference',
        description: 'Scholarly research and reference materials'
      },
      {
        name: 'Language Learning',
        slug: 'language-learning',
        description: 'Books for learning new languages'
      },
      {
        name: 'Professional Development',
        slug: 'professional-development',
        description: 'Books for career and skill development'
      }
    ]
  },
  
  // Children's & Young Adult
  {
    name: "Children's & Young Adult",
    slug: 'childrens-young-adult',
    description: 'Books specifically written for younger readers',
    children: [
      {
        name: "Children's Fiction",
        slug: 'childrens-fiction',
        description: 'Fictional stories for children'
      },
      {
        name: "Children's Non-Fiction",
        slug: 'childrens-non-fiction',
        description: 'Educational and factual books for children'
      },
      {
        name: 'Young Adult Fiction',
        slug: 'young-adult-fiction',
        description: 'Fiction targeted at teenage readers'
      },
      {
        name: 'Picture Books',
        slug: 'picture-books',
        description: 'Illustrated books for young children'
      }
    ]
  },
  
  // Arts & Culture
  {
    name: 'Arts & Culture',
    slug: 'arts-culture',
    description: 'Books about artistic expression and cultural topics',
    children: [
      {
        name: 'Art & Design',
        slug: 'art-design',
        description: 'Books about visual arts, design, and creativity'
      },
      {
        name: 'Music',
        slug: 'music',
        description: 'Books about music, musicians, and musical theory'
      },
      {
        name: 'Film & Television',
        slug: 'film-television',
        description: 'Books about cinema, TV, and media'
      },
      {
        name: 'Philosophy',
        slug: 'philosophy',
        description: 'Books exploring philosophical ideas and concepts'
      },
      {
        name: 'Religion & Spirituality',
        slug: 'religion-spirituality',
        description: 'Books about religious and spiritual topics'
      }
    ]
  }
];

async function seedGenres() {
  console.log('Starting genre seeding...');
  
  try {
    // Clear existing genres
    await prisma.publication.updateMany({
      data: { genreId: null }
    });
    await prisma.genre.deleteMany({});
    
    console.log('Cleared existing genres');
    
    // Create parent genres and their children
    for (const parentGenre of genreData) {
      console.log(`Creating parent genre: ${parentGenre.name}`);
      
      const createdParent = await prisma.genre.create({
        data: {
          name: parentGenre.name,
          slug: parentGenre.slug,
          description: parentGenre.description
        }
      });
      
      // Create child genres
      for (const childGenre of parentGenre.children) {
        console.log(`  Creating child genre: ${childGenre.name}`);
        
        await prisma.genre.create({
          data: {
            name: childGenre.name,
            slug: childGenre.slug,
            description: childGenre.description,
            parentId: createdParent.id
          }
        });
      }
    }
    
    console.log('Genre seeding completed successfully!');
    
    // Display created genres
    const allGenres = await prisma.genre.findMany({
      include: {
        children: true,
        parent: true
      },
      orderBy: [
        { parentId: 'asc' },
        { name: 'asc' }
      ]
    });
    
    console.log('\nCreated genres:');
    allGenres.forEach(genre => {
      if (!genre.parentId) {
        console.log(`\n${genre.name} (${genre.slug})`);
        const children = allGenres.filter(g => g.parentId === genre.id);
        children.forEach(child => {
          console.log(`  - ${child.name} (${child.slug})`);
        });
      }
    });
    
  } catch (error) {
    console.error('Error seeding genres:', error);
    throw error;
  }
}

async function main() {
  try {
    await seedGenres();
  } catch (error) {
    console.error('Seeding failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Node.js ES module entry point check
const isMain = typeof process !== 'undefined' && process.argv[1] === (import.meta.url.replace('file://', ''));
if (isMain) {
  main();
}

export { seedGenres };
