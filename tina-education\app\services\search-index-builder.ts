import { ProcessedDocument } from "../types/content.ts";

export class SearchIndexBuilder {
  buildIndex(documents: ProcessedDocument[]): SearchIndex {
    const index: SearchIndex = {
      documents: {},
      tokenIndex: {},
      ngramIndex: {},
      metadataIndex: {
        types: new Set(),
        authors: new Set(),
        years: new Set(),
      },
    };

    // Process each document
    for (const doc of documents) {
      // Add to documents store
      index.documents[doc.id] = doc;

      // Index by tokens (stemmed words)
      for (const token of doc.stems) {
        index.tokenIndex[token] = index.tokenIndex[token] || new Set();
        index.tokenIndex[token].add(doc.id);
      }

      // Index by ngrams
      for (const ngram of doc.ngrams) {
        index.ngramIndex[ngram] = index.ngramIndex[ngram] || new Set();
        index.ngramIndex[ngram].add(doc.id);
      }

      // Update metadata indexes
      index.metadataIndex.types.add(doc.type);
      doc.authors.forEach((author) => index.metadataIndex.authors.add(author));
      const year = new Date(doc.publicationDate).getFullYear();
      index.metadataIndex.years.add(year);
    }

    return index;
  }
}

export interface SearchIndex {
  documents: Record<string, ProcessedDocument>;
  tokenIndex: Record<string, Set<string>>;
  ngramIndex: Record<string, Set<string>>;
  metadataIndex: {
    types: Set<string>;
    authors: Set<string>;
    years: Set<number>;
  };
}
