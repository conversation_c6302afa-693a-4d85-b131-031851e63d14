Contributions are very welcome. To make the process as easy as possible please follow these steps:

* Open an issue detailing the bug you've found, or the feature you wish to add.  Simplified working examples using something like [jsFiddle](http://jsfiddle.net) make it easier to diagnose your problem.
* Add tests for your code (so I don't accidentally break it in the future).
* Don't change version numbers or make new builds as part of your changes.
* Don't change the built versions of the library; only make changes to code in the `lib` directory.

# Developer Dependencies

A JavaScript runtime is required for building the library.

Run the tests (using PhantomJS):

    make test

The tests can also be run in the browser by starting the test server:

    make server

This will start a server on port 3000, the tests are then available at `/test`.
