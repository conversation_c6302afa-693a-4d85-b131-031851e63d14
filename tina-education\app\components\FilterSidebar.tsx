import React from "react";

interface FilterSidebarProps {
  filterOptions: { [key: string]: string[] };
  filters: { [key: string]: string | null };
  setFilters: React.Dispatch<React.SetStateAction<{ [key: string]: string | null }>>;
}

const FilterSidebar: React.FC<FilterSidebarProps> = ({ filterOptions, filters, setFilters }) => (
  <aside className="w-full md:w-1/4 md:pr-8 mb-6 md:mb-0">
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
      <h3 className="text-lg text-gray-900 font-bold mb-4">Filter By</h3>
      {Object.entries(filterOptions).map(([filter, options]) => (
        <div key={filter} className="mb-4">
          <label className="block text-gray-700 text-sm mb-2">{filter}</label>
          <select
            className="w-full border text-gray-600 border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-300"
            value={filters[filter] || ""}
            onChange={e => setFilters(f => ({ ...f, [filter]: e.target.value || null }))}
          >
            <option value="">All</option>
            {options.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        </div>
      ))}
    </div>
  </aside>
);

export default FilterSidebar;
