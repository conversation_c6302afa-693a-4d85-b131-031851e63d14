import { SearchIndex } from "../types/search.ts";
import { ProcessedDocument } from "../types/content.ts";
import {
  SearchOptions,
  SearchFilters,
  ScoredDocument,
  SearchResults,
  Suggestion,
} from "../types/search.ts";

export class SearchEngine {
  constructor(private index: SearchIndex) {}

  search(query: string, options: SearchOptions = {}): SearchResults {
    // Normalize and tokenize the query
    const queryTokens = this.tokenizeQuery(query);

    // Get matching document IDs
    let resultIds = this.getMatchingDocuments(queryTokens);

    // Apply filters if specified
    if (options.filters) {
      resultIds = this.applyFilters(resultIds, options.filters);
    }

    // Convert IDs to documents and calculate scores
    const results = this.scoreDocuments(
      Array.from(resultIds).map((id) => this.index.documents[id]),
      queryTokens,
      options
    );

    // Sort by score and apply pagination
    return this.paginateResults(results, options);
  }

  private tokenizeQuery(query: string): string[] {
    // Use the same tokenization as during indexing
    const normalized = query
      .toLowerCase()
      .replace(/[^\w\s-]/g, "")
      .replace(/\s+/g, " ")
      .trim();

    const tokens = normalized.split(" ");
    return tokens.map((token) => {
      // Simple stemmer for the query (could use the same stemmer as indexing)
      if (token.endsWith("s")) return token.slice(0, -1);
      if (token.endsWith("ing")) return token.slice(0, -3);
      return token;
    });
  }

  private getMatchingDocuments(queryTokens: string[]): Set<string> {
    if (queryTokens.length === 0) return new Set();

    // Start with documents matching the first token
    let results = new Set<string>();
    for (const token of queryTokens) {
      const tokenMatches = this.index.tokenIndex[token] || new Set();
      const ngramMatches = this.getNgramMatches(token);

      // Combine matches for this token
      const combinedMatches = new Set([...tokenMatches, ...ngramMatches]);

      // For the first token, initialize results
      if (results.size === 0) {
        results = new Set(combinedMatches);
      } else {
        // For subsequent tokens, intersect with existing results
        results = new Set([...results].filter((id) => combinedMatches.has(id)));
      }

      // Early exit if no matches
      if (results.size === 0) break;
    }

    return results;
  }

  private getNgramMatches(token: string): Set<string> {
    // Find ngrams that contain the token (for partial matching)
    const matchingNgrams = Object.keys(this.index.ngramIndex).filter((ngram) =>
      ngram.includes(token)
    );

    return matchingNgrams.reduce((acc, ngram) => {
      const docs = this.index.ngramIndex[ngram] || new Set();
      return new Set([...acc, ...docs]);
    }, new Set<string>());
  }

  private applyFilters(ids: Set<string>, filters: SearchFilters): Set<string> {
    return new Set(
      [...ids].filter((id) => {
        const doc = this.index.documents[id];

        // Type filter
        if (
          filters.types &&
          filters.types.length > 0 &&
          !filters.types.includes(doc.type)
        ) {
          return false;
        }

        // Author filter
        if (
          filters.authors &&
          filters.authors.length > 0 &&
          !doc.authors.some((author) => filters.authors!.includes(author))
        ) {
          return false;
        }

        // Year range filter
        if (filters.yearRange) {
          const year = new Date(doc.publicationDate).getFullYear();
          if (
            (filters.yearRange.min && year < filters.yearRange.min) ||
            (filters.yearRange.max && year > filters.yearRange.max)
          ) {
            return false;
          }
        }

        return true;
      })
    );
  }

  private scoreDocuments(
    docs: ProcessedDocument[],
    queryTokens: string[],
    options: SearchOptions
  ): ScoredDocument[] {
    return docs.map((doc) => {
      let score = 0;

      // Term frequency scoring
      const content = doc.searchableText.toLowerCase();
      for (const token of queryTokens) {
        const regex = new RegExp(`\\b${token}\\b`, "g");
        const matches = content.match(regex);
        const frequency = matches ? matches.length : 0;
        score += frequency * 2; // Higher weight for exact matches

        // Partial matches
        if (content.includes(token)) {
          score += 1;
        }
      }

      // Field-specific boosts
      if (doc.title.toLowerCase().includes(queryTokens.join(" "))) {
        score += 10; // Big boost for title matches
      }

      // Apply document boost (from preprocessing)
      score *= doc.boost;

      // Freshness boost (optional)
      if (options.boostRecent) {
        const ageInYears =
          (Date.now() - new Date(doc.publicationDate).getTime()) /
          (1000 * 60 * 60 * 24 * 365);
        score *= Math.max(0.5, 2 - ageInYears * 0.1);
      }

      return { document: doc, score };
    });
  }

  private paginateResults(
    results: ScoredDocument[],
    options: SearchOptions
  ): SearchResults {
    // Sort by score (descending)
    const sorted = results.sort((a, b) => b.score - a.score);

    // Apply pagination
    const page = options.page || 1;
    const perPage = options.perPage || 10;
    const start = (page - 1) * perPage;
    const end = start + perPage;

    return {
      results: sorted.slice(start, end),
      total: sorted.length,
      page,
      perPage,
      totalPages: Math.ceil(sorted.length / perPage),
    };
  }

  // Helper method for autocomplete
  getSuggestions(query: string, limit = 5): Suggestion[] {
    if (!query) return [];

    const normalized = query.toLowerCase();
    const suggestions = new Map<string, number>();

    // Find matching tokens
    for (const token of Object.keys(this.index.tokenIndex)) {
      if (token.startsWith(normalized)) {
        const docs = this.index.tokenIndex[token];
        suggestions.set(token, docs.size);
      }
    }

    // Find matching ngrams
    for (const ngram of Object.keys(this.index.ngramIndex)) {
      if (ngram.startsWith(normalized)) {
        const docs = this.index.ngramIndex[ngram];
        const currentCount = suggestions.get(ngram) || 0;
        suggestions.set(ngram, currentCount + docs.size);
      }
    }

    // Convert to array and sort by relevance
    return Array.from(suggestions.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
      .map(([text, count]) => ({ text, count }));
  }
}
