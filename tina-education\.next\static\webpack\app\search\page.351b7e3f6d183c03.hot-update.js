"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/search/page",{

/***/ "(app-pages-browser)/./app/search/page.tsx":
/*!*****************************!*\
  !*** ./app/search/page.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _useLunrSearch__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useLunrSearch */ \"(app-pages-browser)/./app/search/useLunrSearch.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_SortBar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/SortBar */ \"(app-pages-browser)/./app/components/SortBar.tsx\");\n/* harmony import */ var _components_FilterSidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/FilterSidebar */ \"(app-pages-browser)/./app/components/FilterSidebar.tsx\");\n/* harmony import */ var _components_ResultCard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/ResultCard */ \"(app-pages-browser)/./app/components/ResultCard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst filterOptions = {\n    Author: [\n        \"Gillian Flynn\",\n        \"Alex Michaelides\",\n        \"Stieg Larsson\",\n        \"Liane Moriarty\",\n        \"A.J. Finn\"\n    ],\n    Category: [\n        \"Mystery\",\n        \"Thriller\",\n        \"Drama\"\n    ],\n    Imprint: [\n        \"Tina Publishing\",\n        \"Other Imprint\"\n    ],\n    \"Product Type\": [\n        \"Paperback\",\n        \"Hardback\",\n        \"eBook\",\n        \"Audiobook\"\n    ],\n    Series: [\n        \"Series 1\",\n        \"Series 2\"\n    ]\n};\n// Helper: Convert SearchFilters to sidebar filters\nfunction filtersToSidebar(filters) {\n    var _filters_authors, _filters_types;\n    return {\n        Author: ((_filters_authors = filters.authors) === null || _filters_authors === void 0 ? void 0 : _filters_authors[0]) || null,\n        Category: ((_filters_types = filters.types) === null || _filters_types === void 0 ? void 0 : _filters_types[0]) || null\n    };\n}\n// Helper: Convert sidebar filters to SearchFilters\nfunction sidebarToFilters(sidebar) {\n    return {\n        authors: sidebar.Author ? [\n            sidebar.Author\n        ] : undefined,\n        types: sidebar.Category ? [\n            sidebar.Category\n        ] : undefined\n    };\n}\n// Helper: Map ProcessedDocument to ResultCard shape\nfunction mapToResultCard(doc) {\n    var _doc_authors, _doc_keywords;\n    return {\n        id: doc.id,\n        title: doc.title,\n        author: ((_doc_authors = doc.authors) === null || _doc_authors === void 0 ? void 0 : _doc_authors[0]) || \"Unknown Author\",\n        price: doc.price || \"-\",\n        type: doc.type,\n        cover: doc.cover || \"/images/book-placeholder.jpg\",\n        imprint: doc.publisher || doc.imprint || \"\",\n        category: doc.category || ((_doc_keywords = doc.keywords) === null || _doc_keywords === void 0 ? void 0 : _doc_keywords[0]) || doc.normalizedType || \"\",\n        series: doc.series || \"\"\n    };\n}\nconst SearchPage = ()=>{\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { query, setQuery, filters, setFilters, results, isSearching, loading, error } = (0,_useLunrSearch__WEBPACK_IMPORTED_MODULE_3__.useLunrSearch)();\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isInitialLoad, setIsInitialLoad] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sort, setSort] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"relevance\");\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const resultsPerPage = 10;\n    const [sidebarFilters, setSidebarFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(filtersToSidebar(filters));\n    // Initialize from URL on first load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SearchPage.useEffect\": ()=>{\n            if (isInitialLoad && searchParams) {\n                var _searchParams_get;\n                const urlQuery = searchParams.get(\"q\") || \"\";\n                const urlTypes = ((_searchParams_get = searchParams.get(\"types\")) === null || _searchParams_get === void 0 ? void 0 : _searchParams_get.split(\",\")) || [];\n                const minYear = searchParams.get(\"minYear\");\n                const maxYear = searchParams.get(\"maxYear\");\n                setQuery(urlQuery);\n                setFilters({\n                    types: urlTypes,\n                    yearRange: {\n                        min: minYear ? parseInt(minYear) : undefined,\n                        max: maxYear ? parseInt(maxYear) : undefined\n                    }\n                });\n                setIsInitialLoad(false);\n            }\n        }\n    }[\"SearchPage.useEffect\"], [\n        searchParams,\n        isInitialLoad,\n        setQuery,\n        setFilters\n    ]);\n    // Update page in search options\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SearchPage.useEffect\": ()=>{\n        // If your useSearch supports page/perPage, you can pass them here\n        // Otherwise, filter results manually below\n        }\n    }[\"SearchPage.useEffect\"], [\n        page\n    ]);\n    // Pagination logic\n    const totalResults = (results === null || results === void 0 ? void 0 : results.total) || 0;\n    const pageCount = (results === null || results === void 0 ? void 0 : results.totalPages) || Math.ceil(totalResults / resultsPerPage);\n    const currentPage = (results === null || results === void 0 ? void 0 : results.page) || page;\n    const pageInfo = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-2 text-gray-600\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"px-2 py-1 rounded border\",\n                disabled: currentPage === 1,\n                onClick: ()=>setPage((p)=>Math.max(1, p - 1)),\n                children: \"<\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: [\n                    \"Page \",\n                    currentPage,\n                    \" of \",\n                    pageCount\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"px-2 py-1 rounded border\",\n                disabled: currentPage === pageCount,\n                onClick: ()=>setPage((p)=>Math.min(pageCount, p + 1)),\n                children: \">\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, undefined);\n    // Search input handler\n    const handleSearchInput = (e)=>{\n        setQuery(e.target.value);\n        setPage(1);\n    };\n    // Keep sidebar filters in sync with search filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SearchPage.useEffect\": ()=>{\n            setSidebarFilters(filtersToSidebar(filters));\n        }\n    }[\"SearchPage.useEffect\"], [\n        filters\n    ]);\n    // When sidebar filters change, update search filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SearchPage.useEffect\": ()=>{\n            setFilters(sidebarToFilters(sidebarFilters));\n            setPage(1);\n        }\n    }[\"SearchPage.useEffect\"], [\n        sidebarFilters\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen mt-19  bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 md:px-10 py-4 border-b border-blue-500 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"text-s text-black\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                            href: \"/\",\n                            className: \"hover:underline\",\n                            children: \"Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"mx-2\",\n                            children: \"/\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Search Results\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 md:px-10 py-6 bg-white border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-black mb-2\",\n                        children: [\n                            'Search Results for \"',\n                            query,\n                            '\"'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-700 text-sm\",\n                        children: isSearching ? \"Searching...\" : \"\".concat(totalResults, \" results found\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        className: \"mt-4 text-gray-500 border rounded px-3 py-2 w-full max-w-md\",\n                        placeholder: \"Search for books, articles, journals...\",\n                        value: query,\n                        onChange: handleSearchInput\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 md:px-10 mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SortBar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    displayInfo: \"Displaying \".concat((currentPage - 1) * resultsPerPage + 1, \"-\").concat(Math.min(currentPage * resultsPerPage, totalResults), \" of \").concat(totalResults, \" results\"),\n                    showSort: true,\n                    sortValue: sort,\n                    onSortChange: setSort,\n                    pageInfo: pageInfo\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 md:px-10 flex flex-col md:flex-row gap-8 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FilterSidebar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        filterOptions: filterOptions,\n                        filters: sidebarFilters,\n                        setFilters: setSidebarFilters\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"w-full text-gray-600 md:w-3/4\",\n                        children: [\n                            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"Loading Search Results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 23\n                            }, undefined),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-red-500\",\n                                children: \"Error loading Search Results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, undefined),\n                            isSearching && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"Searching for Results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 27\n                            }, undefined),\n                            !isSearching && results && results.results.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"No results found.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, undefined),\n                            !isSearching && results && results.results.map((document)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResultCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    result: mapToResultCard(document)\n                                }, document.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, undefined))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 md:px-10 mt-8 pb-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SortBar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    displayInfo: \"Displaying \".concat((currentPage - 1) * resultsPerPage + 1, \"-\").concat(Math.min(currentPage * resultsPerPage, totalResults), \" of \").concat(totalResults, \" results\"),\n                    showSort: false,\n                    pageInfo: pageInfo\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\search\\\\page.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SearchPage, \"xOWbCA5Wdln85gkwHbyjgY/nCys=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _useLunrSearch__WEBPACK_IMPORTED_MODULE_3__.useLunrSearch\n    ];\n});\n_c = SearchPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SearchPage);\nvar _c;\n$RefreshReg$(_c, \"SearchPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/search/page.tsx\n"));

/***/ })

});