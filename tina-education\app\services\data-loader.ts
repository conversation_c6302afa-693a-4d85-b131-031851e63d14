import path from "path";
import fs from "fs/promises";
import { ContentItem } from "../types/content.ts";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export class DataLoader {
  private contentPath: string;

  constructor(contentPath: string) {
    this.contentPath = path.join(process.cwd(), contentPath);
  }

  async loadAllContent(): Promise<ContentItem[]> {
    // Try to load from database first
    try {
      // Fetch publications (books, journals, articles) from DB
      const publications = await prisma.publication.findMany({
        include: {
          user: true,
        },
      });
      // Map DB results to ContentItem[]
      const dbContent: ContentItem[] = publications.map((pub) => {
        // Map type
        let type: ContentItem["type"] = "book";
        if (pub.type === "JOURNAL") type = "journal";
        if (pub.type === "ARTICLE") type = "article";
        // Map authors (assuming user relation)
        const authors = pub.user && pub.user.name ? [pub.user.name] : ["Unknown Author"];
        // Map keywords (comma separated string to array)
        const keywords = pub.keywords ? pub.keywords.split(",").map(k => k.trim()) : [];
        // Map fields
        return {
          id: pub.id,
          type,
          title: pub.title,
          authors,
          publicationDate: pub.createdAt.toISOString(),
          keywords,
          // Add other fields as needed
        } as ContentItem;
      });
      if (dbContent.length > 0) return dbContent;
    } catch (err) {
      console.error("Error loading from database, falling back to file system:", err);
    }
    // Fallback to file system
    const types = ["books", "journals", "articles"];
    const allContent: ContentItem[] = [];

    for (const type of types) {
      const typePath = path.join(this.contentPath, type);
      try {
        const files = await fs.readdir(typePath);
        const typeContent = await Promise.all(
          files
            .filter((file) => file.endsWith(".json"))
            .map(async (file) => {
              const data = await fs.readFile(
                path.join(typePath, file),
                "utf-8"
              );
              return JSON.parse(data) as ContentItem;
            })
        );
        allContent.push(...typeContent);
      } catch (err) {
        console.error(`Error loading ${type}:`, err);
      }
    }

    return allContent;
  }
}
