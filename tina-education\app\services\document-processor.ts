// services/document-processor.ts
import { ContentItem, ProcessedDocument } from "../types/content.ts";
import {
  normalizeText,
  tokenizeText,
  stemTokens,
  generateNGrams,
} from "../utils/text-processor.ts";

export class DocumentProcessor {
  processDocument(doc: ContentItem): ProcessedDocument {
    // Basic normalization
    const processed: any = {
      ...doc,
      title: normalizeText(doc.title || ""),
      authors: Array.isArray(doc.authors)
        ? doc.authors.map((a) => normalizeText(a || ""))
        : [],
      normalizedType: (doc.type || "").toUpperCase(),
    };

    // Process content-specific fields
    if (doc.type === "article") {
      processed.abstract = normalizeText(doc.abstract || "");
    }

    // Generate searchable text
    const searchableFields = [
      processed.title,
      ...processed.authors,
      ...(Array.isArray(doc.keywords)
        ? doc.keywords.map((k) => normalizeText(k || ""))
        : []),
      processed.abstract || "",
    ];

    processed.searchableText = searchableFields.join(" ");

    // Tokenization and stemming
    const tokens = tokenizeText(processed.searchableText);
    processed.tokens = tokens;
    processed.stems = stemTokens(tokens);

    // Generate n-grams for partial matching
    processed.ngrams = generateNGrams(processed.searchableText);

    // Add metadata for boosting
    processed.boost = this.calculateBoost(doc);

    return processed as ProcessedDocument;
  }

  private calculateBoost(doc: ContentItem): number {
    let boost = 1.0;

    // Boost books slightly more
    if (doc.type === "book") boost *= 1.2;

    // Boost newer publications
    const pubDate = new Date(doc.publicationDate);
    const ageInYears =
      (Date.now() - pubDate.getTime()) / (1000 * 60 * 60 * 24 * 365);
    boost *= Math.max(0.7, 1.5 - ageInYears * 0.1);

    return boost;
  }
}


