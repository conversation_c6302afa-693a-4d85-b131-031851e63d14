'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSearch } from '../hooks/useSearch';
import { Suggestion } from '../types/search';
import { IoIosSearch } from 'react-icons/io';

export function SearchBox() {
  const [localQuery, setLocalQuery] = useState('');
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [inputFocused, setInputFocused] = useState(false);
  const router = useRouter();
  const { getSuggestions } = useSearch();

  useEffect(() => {
    if (!localQuery || !inputFocused) {
      setSuggestions([]);
      return;
    }
    
    const timer = setTimeout(() => {
      setSuggestions(getSuggestions(localQuery));
    }, 200);
    
    return () => clearTimeout(timer);
  }, [localQuery, inputFocused, getSuggestions]);

  const handleSearch = () => {
    if (localQuery.trim()) {
      // Construct the URL manually for next/navigation router
      router.push(`/search?q=${encodeURIComponent(localQuery)}`);
    }
  };

  return (
    <div className="max-w-3xl mx-auto ">
      <div className="relative">
        <input
          type="text"
          value={localQuery}
          onChange={(e) => setLocalQuery(e.target.value)}
          onFocus={() => setInputFocused(true)}
          onBlur={() => setTimeout(() => setInputFocused(false), 200)}
          onKeyDown={(e) => e.key === "Enter" && handleSearch()}
          placeholder="Search Authors, Books and Journals..."
          className="w-full px-6 py-4 rounded-lg text-base text-gray-800 bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 shadow-lg"
          aria-label="Search content"
        />
        <button
          onClick={handleSearch}
          className="absolute hidden sm:block right-2 top-2 px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 transition-colors"
          disabled={!localQuery.trim()}
        >
          Search
        </button>
        <span className="block md:hidden absolute right-2 top-2 text-gray-400 py-2 text-3xl">
          <IoIosSearch />
        </span>
      </div>

      {suggestions.length > 0 && inputFocused && (
        <ul className="absolute z-10 w-full mt-1 bg-white border rounded-lg shadow-lg max-h-60 overflow-auto">
          {suggestions.map((suggestion) => (
            <li
              key={suggestion.text}
              className="p-3 hover:bg-gray-100 cursor-pointer"
              onMouseDown={(e) => e.preventDefault()} // Prevent immediate blur
              onClick={() => {
                setLocalQuery(suggestion.text);
                handleSearch();
              }}
            >
              {suggestion.text}
              <span className="text-gray-500 text-sm ml-2">
                ({suggestion.count})
              </span>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}