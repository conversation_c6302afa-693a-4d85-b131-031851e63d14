/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lunr";
exports.ids = ["vendor-chunks/lunr"];
exports.modules = {

/***/ "(ssr)/./node_modules/lunr/lunr.js":
/*!***********************************!*\
  !*** ./node_modules/lunr/lunr.js ***!
  \***********************************/
/***/ ((module, exports, __webpack_require__) => {

eval("var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_RESULT__;/**\n * lunr - http://lunrjs.com - A bit like Solr, but much smaller and not as bright - 2.3.9\n * Copyright (C) 2020 Oliver Nightingale\n * @license MIT\n */\n\n;(function(){\n\n/**\n * A convenience function for configuring and constructing\n * a new lunr Index.\n *\n * A lunr.Builder instance is created and the pipeline setup\n * with a trimmer, stop word filter and stemmer.\n *\n * This builder object is yielded to the configuration function\n * that is passed as a parameter, allowing the list of fields\n * and other builder parameters to be customised.\n *\n * All documents _must_ be added within the passed config function.\n *\n * @example\n * var idx = lunr(function () {\n *   this.field('title')\n *   this.field('body')\n *   this.ref('id')\n *\n *   documents.forEach(function (doc) {\n *     this.add(doc)\n *   }, this)\n * })\n *\n * @see {@link lunr.Builder}\n * @see {@link lunr.Pipeline}\n * @see {@link lunr.trimmer}\n * @see {@link lunr.stopWordFilter}\n * @see {@link lunr.stemmer}\n * @namespace {function} lunr\n */\nvar lunr = function (config) {\n  var builder = new lunr.Builder\n\n  builder.pipeline.add(\n    lunr.trimmer,\n    lunr.stopWordFilter,\n    lunr.stemmer\n  )\n\n  builder.searchPipeline.add(\n    lunr.stemmer\n  )\n\n  config.call(builder, builder)\n  return builder.build()\n}\n\nlunr.version = \"2.3.9\"\n/*!\n * lunr.utils\n * Copyright (C) 2020 Oliver Nightingale\n */\n\n/**\n * A namespace containing utils for the rest of the lunr library\n * @namespace lunr.utils\n */\nlunr.utils = {}\n\n/**\n * Print a warning message to the console.\n *\n * @param {String} message The message to be printed.\n * @memberOf lunr.utils\n * @function\n */\nlunr.utils.warn = (function (global) {\n  /* eslint-disable no-console */\n  return function (message) {\n    if (global.console && console.warn) {\n      console.warn(message)\n    }\n  }\n  /* eslint-enable no-console */\n})(this)\n\n/**\n * Convert an object to a string.\n *\n * In the case of `null` and `undefined` the function returns\n * the empty string, in all other cases the result of calling\n * `toString` on the passed object is returned.\n *\n * @param {Any} obj The object to convert to a string.\n * @return {String} string representation of the passed object.\n * @memberOf lunr.utils\n */\nlunr.utils.asString = function (obj) {\n  if (obj === void 0 || obj === null) {\n    return \"\"\n  } else {\n    return obj.toString()\n  }\n}\n\n/**\n * Clones an object.\n *\n * Will create a copy of an existing object such that any mutations\n * on the copy cannot affect the original.\n *\n * Only shallow objects are supported, passing a nested object to this\n * function will cause a TypeError.\n *\n * Objects with primitives, and arrays of primitives are supported.\n *\n * @param {Object} obj The object to clone.\n * @return {Object} a clone of the passed object.\n * @throws {TypeError} when a nested object is passed.\n * @memberOf Utils\n */\nlunr.utils.clone = function (obj) {\n  if (obj === null || obj === undefined) {\n    return obj\n  }\n\n  var clone = Object.create(null),\n      keys = Object.keys(obj)\n\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i],\n        val = obj[key]\n\n    if (Array.isArray(val)) {\n      clone[key] = val.slice()\n      continue\n    }\n\n    if (typeof val === 'string' ||\n        typeof val === 'number' ||\n        typeof val === 'boolean') {\n      clone[key] = val\n      continue\n    }\n\n    throw new TypeError(\"clone is not deep and does not support nested objects\")\n  }\n\n  return clone\n}\nlunr.FieldRef = function (docRef, fieldName, stringValue) {\n  this.docRef = docRef\n  this.fieldName = fieldName\n  this._stringValue = stringValue\n}\n\nlunr.FieldRef.joiner = \"/\"\n\nlunr.FieldRef.fromString = function (s) {\n  var n = s.indexOf(lunr.FieldRef.joiner)\n\n  if (n === -1) {\n    throw \"malformed field ref string\"\n  }\n\n  var fieldRef = s.slice(0, n),\n      docRef = s.slice(n + 1)\n\n  return new lunr.FieldRef (docRef, fieldRef, s)\n}\n\nlunr.FieldRef.prototype.toString = function () {\n  if (this._stringValue == undefined) {\n    this._stringValue = this.fieldName + lunr.FieldRef.joiner + this.docRef\n  }\n\n  return this._stringValue\n}\n/*!\n * lunr.Set\n * Copyright (C) 2020 Oliver Nightingale\n */\n\n/**\n * A lunr set.\n *\n * @constructor\n */\nlunr.Set = function (elements) {\n  this.elements = Object.create(null)\n\n  if (elements) {\n    this.length = elements.length\n\n    for (var i = 0; i < this.length; i++) {\n      this.elements[elements[i]] = true\n    }\n  } else {\n    this.length = 0\n  }\n}\n\n/**\n * A complete set that contains all elements.\n *\n * @static\n * @readonly\n * @type {lunr.Set}\n */\nlunr.Set.complete = {\n  intersect: function (other) {\n    return other\n  },\n\n  union: function () {\n    return this\n  },\n\n  contains: function () {\n    return true\n  }\n}\n\n/**\n * An empty set that contains no elements.\n *\n * @static\n * @readonly\n * @type {lunr.Set}\n */\nlunr.Set.empty = {\n  intersect: function () {\n    return this\n  },\n\n  union: function (other) {\n    return other\n  },\n\n  contains: function () {\n    return false\n  }\n}\n\n/**\n * Returns true if this set contains the specified object.\n *\n * @param {object} object - Object whose presence in this set is to be tested.\n * @returns {boolean} - True if this set contains the specified object.\n */\nlunr.Set.prototype.contains = function (object) {\n  return !!this.elements[object]\n}\n\n/**\n * Returns a new set containing only the elements that are present in both\n * this set and the specified set.\n *\n * @param {lunr.Set} other - set to intersect with this set.\n * @returns {lunr.Set} a new set that is the intersection of this and the specified set.\n */\n\nlunr.Set.prototype.intersect = function (other) {\n  var a, b, elements, intersection = []\n\n  if (other === lunr.Set.complete) {\n    return this\n  }\n\n  if (other === lunr.Set.empty) {\n    return other\n  }\n\n  if (this.length < other.length) {\n    a = this\n    b = other\n  } else {\n    a = other\n    b = this\n  }\n\n  elements = Object.keys(a.elements)\n\n  for (var i = 0; i < elements.length; i++) {\n    var element = elements[i]\n    if (element in b.elements) {\n      intersection.push(element)\n    }\n  }\n\n  return new lunr.Set (intersection)\n}\n\n/**\n * Returns a new set combining the elements of this and the specified set.\n *\n * @param {lunr.Set} other - set to union with this set.\n * @return {lunr.Set} a new set that is the union of this and the specified set.\n */\n\nlunr.Set.prototype.union = function (other) {\n  if (other === lunr.Set.complete) {\n    return lunr.Set.complete\n  }\n\n  if (other === lunr.Set.empty) {\n    return this\n  }\n\n  return new lunr.Set(Object.keys(this.elements).concat(Object.keys(other.elements)))\n}\n/**\n * A function to calculate the inverse document frequency for\n * a posting. This is shared between the builder and the index\n *\n * @private\n * @param {object} posting - The posting for a given term\n * @param {number} documentCount - The total number of documents.\n */\nlunr.idf = function (posting, documentCount) {\n  var documentsWithTerm = 0\n\n  for (var fieldName in posting) {\n    if (fieldName == '_index') continue // Ignore the term index, its not a field\n    documentsWithTerm += Object.keys(posting[fieldName]).length\n  }\n\n  var x = (documentCount - documentsWithTerm + 0.5) / (documentsWithTerm + 0.5)\n\n  return Math.log(1 + Math.abs(x))\n}\n\n/**\n * A token wraps a string representation of a token\n * as it is passed through the text processing pipeline.\n *\n * @constructor\n * @param {string} [str=''] - The string token being wrapped.\n * @param {object} [metadata={}] - Metadata associated with this token.\n */\nlunr.Token = function (str, metadata) {\n  this.str = str || \"\"\n  this.metadata = metadata || {}\n}\n\n/**\n * Returns the token string that is being wrapped by this object.\n *\n * @returns {string}\n */\nlunr.Token.prototype.toString = function () {\n  return this.str\n}\n\n/**\n * A token update function is used when updating or optionally\n * when cloning a token.\n *\n * @callback lunr.Token~updateFunction\n * @param {string} str - The string representation of the token.\n * @param {Object} metadata - All metadata associated with this token.\n */\n\n/**\n * Applies the given function to the wrapped string token.\n *\n * @example\n * token.update(function (str, metadata) {\n *   return str.toUpperCase()\n * })\n *\n * @param {lunr.Token~updateFunction} fn - A function to apply to the token string.\n * @returns {lunr.Token}\n */\nlunr.Token.prototype.update = function (fn) {\n  this.str = fn(this.str, this.metadata)\n  return this\n}\n\n/**\n * Creates a clone of this token. Optionally a function can be\n * applied to the cloned token.\n *\n * @param {lunr.Token~updateFunction} [fn] - An optional function to apply to the cloned token.\n * @returns {lunr.Token}\n */\nlunr.Token.prototype.clone = function (fn) {\n  fn = fn || function (s) { return s }\n  return new lunr.Token (fn(this.str, this.metadata), this.metadata)\n}\n/*!\n * lunr.tokenizer\n * Copyright (C) 2020 Oliver Nightingale\n */\n\n/**\n * A function for splitting a string into tokens ready to be inserted into\n * the search index. Uses `lunr.tokenizer.separator` to split strings, change\n * the value of this property to change how strings are split into tokens.\n *\n * This tokenizer will convert its parameter to a string by calling `toString` and\n * then will split this string on the character in `lunr.tokenizer.separator`.\n * Arrays will have their elements converted to strings and wrapped in a lunr.Token.\n *\n * Optional metadata can be passed to the tokenizer, this metadata will be cloned and\n * added as metadata to every token that is created from the object to be tokenized.\n *\n * @static\n * @param {?(string|object|object[])} obj - The object to convert into tokens\n * @param {?object} metadata - Optional metadata to associate with every token\n * @returns {lunr.Token[]}\n * @see {@link lunr.Pipeline}\n */\nlunr.tokenizer = function (obj, metadata) {\n  if (obj == null || obj == undefined) {\n    return []\n  }\n\n  if (Array.isArray(obj)) {\n    return obj.map(function (t) {\n      return new lunr.Token(\n        lunr.utils.asString(t).toLowerCase(),\n        lunr.utils.clone(metadata)\n      )\n    })\n  }\n\n  var str = obj.toString().toLowerCase(),\n      len = str.length,\n      tokens = []\n\n  for (var sliceEnd = 0, sliceStart = 0; sliceEnd <= len; sliceEnd++) {\n    var char = str.charAt(sliceEnd),\n        sliceLength = sliceEnd - sliceStart\n\n    if ((char.match(lunr.tokenizer.separator) || sliceEnd == len)) {\n\n      if (sliceLength > 0) {\n        var tokenMetadata = lunr.utils.clone(metadata) || {}\n        tokenMetadata[\"position\"] = [sliceStart, sliceLength]\n        tokenMetadata[\"index\"] = tokens.length\n\n        tokens.push(\n          new lunr.Token (\n            str.slice(sliceStart, sliceEnd),\n            tokenMetadata\n          )\n        )\n      }\n\n      sliceStart = sliceEnd + 1\n    }\n\n  }\n\n  return tokens\n}\n\n/**\n * The separator used to split a string into tokens. Override this property to change the behaviour of\n * `lunr.tokenizer` behaviour when tokenizing strings. By default this splits on whitespace and hyphens.\n *\n * @static\n * @see lunr.tokenizer\n */\nlunr.tokenizer.separator = /[\\s\\-]+/\n/*!\n * lunr.Pipeline\n * Copyright (C) 2020 Oliver Nightingale\n */\n\n/**\n * lunr.Pipelines maintain an ordered list of functions to be applied to all\n * tokens in documents entering the search index and queries being ran against\n * the index.\n *\n * An instance of lunr.Index created with the lunr shortcut will contain a\n * pipeline with a stop word filter and an English language stemmer. Extra\n * functions can be added before or after either of these functions or these\n * default functions can be removed.\n *\n * When run the pipeline will call each function in turn, passing a token, the\n * index of that token in the original list of all tokens and finally a list of\n * all the original tokens.\n *\n * The output of functions in the pipeline will be passed to the next function\n * in the pipeline. To exclude a token from entering the index the function\n * should return undefined, the rest of the pipeline will not be called with\n * this token.\n *\n * For serialisation of pipelines to work, all functions used in an instance of\n * a pipeline should be registered with lunr.Pipeline. Registered functions can\n * then be loaded. If trying to load a serialised pipeline that uses functions\n * that are not registered an error will be thrown.\n *\n * If not planning on serialising the pipeline then registering pipeline functions\n * is not necessary.\n *\n * @constructor\n */\nlunr.Pipeline = function () {\n  this._stack = []\n}\n\nlunr.Pipeline.registeredFunctions = Object.create(null)\n\n/**\n * A pipeline function maps lunr.Token to lunr.Token. A lunr.Token contains the token\n * string as well as all known metadata. A pipeline function can mutate the token string\n * or mutate (or add) metadata for a given token.\n *\n * A pipeline function can indicate that the passed token should be discarded by returning\n * null, undefined or an empty string. This token will not be passed to any downstream pipeline\n * functions and will not be added to the index.\n *\n * Multiple tokens can be returned by returning an array of tokens. Each token will be passed\n * to any downstream pipeline functions and all will returned tokens will be added to the index.\n *\n * Any number of pipeline functions may be chained together using a lunr.Pipeline.\n *\n * @interface lunr.PipelineFunction\n * @param {lunr.Token} token - A token from the document being processed.\n * @param {number} i - The index of this token in the complete list of tokens for this document/field.\n * @param {lunr.Token[]} tokens - All tokens for this document/field.\n * @returns {(?lunr.Token|lunr.Token[])}\n */\n\n/**\n * Register a function with the pipeline.\n *\n * Functions that are used in the pipeline should be registered if the pipeline\n * needs to be serialised, or a serialised pipeline needs to be loaded.\n *\n * Registering a function does not add it to a pipeline, functions must still be\n * added to instances of the pipeline for them to be used when running a pipeline.\n *\n * @param {lunr.PipelineFunction} fn - The function to check for.\n * @param {String} label - The label to register this function with\n */\nlunr.Pipeline.registerFunction = function (fn, label) {\n  if (label in this.registeredFunctions) {\n    lunr.utils.warn('Overwriting existing registered function: ' + label)\n  }\n\n  fn.label = label\n  lunr.Pipeline.registeredFunctions[fn.label] = fn\n}\n\n/**\n * Warns if the function is not registered as a Pipeline function.\n *\n * @param {lunr.PipelineFunction} fn - The function to check for.\n * @private\n */\nlunr.Pipeline.warnIfFunctionNotRegistered = function (fn) {\n  var isRegistered = fn.label && (fn.label in this.registeredFunctions)\n\n  if (!isRegistered) {\n    lunr.utils.warn('Function is not registered with pipeline. This may cause problems when serialising the index.\\n', fn)\n  }\n}\n\n/**\n * Loads a previously serialised pipeline.\n *\n * All functions to be loaded must already be registered with lunr.Pipeline.\n * If any function from the serialised data has not been registered then an\n * error will be thrown.\n *\n * @param {Object} serialised - The serialised pipeline to load.\n * @returns {lunr.Pipeline}\n */\nlunr.Pipeline.load = function (serialised) {\n  var pipeline = new lunr.Pipeline\n\n  serialised.forEach(function (fnName) {\n    var fn = lunr.Pipeline.registeredFunctions[fnName]\n\n    if (fn) {\n      pipeline.add(fn)\n    } else {\n      throw new Error('Cannot load unregistered function: ' + fnName)\n    }\n  })\n\n  return pipeline\n}\n\n/**\n * Adds new functions to the end of the pipeline.\n *\n * Logs a warning if the function has not been registered.\n *\n * @param {lunr.PipelineFunction[]} functions - Any number of functions to add to the pipeline.\n */\nlunr.Pipeline.prototype.add = function () {\n  var fns = Array.prototype.slice.call(arguments)\n\n  fns.forEach(function (fn) {\n    lunr.Pipeline.warnIfFunctionNotRegistered(fn)\n    this._stack.push(fn)\n  }, this)\n}\n\n/**\n * Adds a single function after a function that already exists in the\n * pipeline.\n *\n * Logs a warning if the function has not been registered.\n *\n * @param {lunr.PipelineFunction} existingFn - A function that already exists in the pipeline.\n * @param {lunr.PipelineFunction} newFn - The new function to add to the pipeline.\n */\nlunr.Pipeline.prototype.after = function (existingFn, newFn) {\n  lunr.Pipeline.warnIfFunctionNotRegistered(newFn)\n\n  var pos = this._stack.indexOf(existingFn)\n  if (pos == -1) {\n    throw new Error('Cannot find existingFn')\n  }\n\n  pos = pos + 1\n  this._stack.splice(pos, 0, newFn)\n}\n\n/**\n * Adds a single function before a function that already exists in the\n * pipeline.\n *\n * Logs a warning if the function has not been registered.\n *\n * @param {lunr.PipelineFunction} existingFn - A function that already exists in the pipeline.\n * @param {lunr.PipelineFunction} newFn - The new function to add to the pipeline.\n */\nlunr.Pipeline.prototype.before = function (existingFn, newFn) {\n  lunr.Pipeline.warnIfFunctionNotRegistered(newFn)\n\n  var pos = this._stack.indexOf(existingFn)\n  if (pos == -1) {\n    throw new Error('Cannot find existingFn')\n  }\n\n  this._stack.splice(pos, 0, newFn)\n}\n\n/**\n * Removes a function from the pipeline.\n *\n * @param {lunr.PipelineFunction} fn The function to remove from the pipeline.\n */\nlunr.Pipeline.prototype.remove = function (fn) {\n  var pos = this._stack.indexOf(fn)\n  if (pos == -1) {\n    return\n  }\n\n  this._stack.splice(pos, 1)\n}\n\n/**\n * Runs the current list of functions that make up the pipeline against the\n * passed tokens.\n *\n * @param {Array} tokens The tokens to run through the pipeline.\n * @returns {Array}\n */\nlunr.Pipeline.prototype.run = function (tokens) {\n  var stackLength = this._stack.length\n\n  for (var i = 0; i < stackLength; i++) {\n    var fn = this._stack[i]\n    var memo = []\n\n    for (var j = 0; j < tokens.length; j++) {\n      var result = fn(tokens[j], j, tokens)\n\n      if (result === null || result === void 0 || result === '') continue\n\n      if (Array.isArray(result)) {\n        for (var k = 0; k < result.length; k++) {\n          memo.push(result[k])\n        }\n      } else {\n        memo.push(result)\n      }\n    }\n\n    tokens = memo\n  }\n\n  return tokens\n}\n\n/**\n * Convenience method for passing a string through a pipeline and getting\n * strings out. This method takes care of wrapping the passed string in a\n * token and mapping the resulting tokens back to strings.\n *\n * @param {string} str - The string to pass through the pipeline.\n * @param {?object} metadata - Optional metadata to associate with the token\n * passed to the pipeline.\n * @returns {string[]}\n */\nlunr.Pipeline.prototype.runString = function (str, metadata) {\n  var token = new lunr.Token (str, metadata)\n\n  return this.run([token]).map(function (t) {\n    return t.toString()\n  })\n}\n\n/**\n * Resets the pipeline by removing any existing processors.\n *\n */\nlunr.Pipeline.prototype.reset = function () {\n  this._stack = []\n}\n\n/**\n * Returns a representation of the pipeline ready for serialisation.\n *\n * Logs a warning if the function has not been registered.\n *\n * @returns {Array}\n */\nlunr.Pipeline.prototype.toJSON = function () {\n  return this._stack.map(function (fn) {\n    lunr.Pipeline.warnIfFunctionNotRegistered(fn)\n\n    return fn.label\n  })\n}\n/*!\n * lunr.Vector\n * Copyright (C) 2020 Oliver Nightingale\n */\n\n/**\n * A vector is used to construct the vector space of documents and queries. These\n * vectors support operations to determine the similarity between two documents or\n * a document and a query.\n *\n * Normally no parameters are required for initializing a vector, but in the case of\n * loading a previously dumped vector the raw elements can be provided to the constructor.\n *\n * For performance reasons vectors are implemented with a flat array, where an elements\n * index is immediately followed by its value. E.g. [index, value, index, value]. This\n * allows the underlying array to be as sparse as possible and still offer decent\n * performance when being used for vector calculations.\n *\n * @constructor\n * @param {Number[]} [elements] - The flat list of element index and element value pairs.\n */\nlunr.Vector = function (elements) {\n  this._magnitude = 0\n  this.elements = elements || []\n}\n\n\n/**\n * Calculates the position within the vector to insert a given index.\n *\n * This is used internally by insert and upsert. If there are duplicate indexes then\n * the position is returned as if the value for that index were to be updated, but it\n * is the callers responsibility to check whether there is a duplicate at that index\n *\n * @param {Number} insertIdx - The index at which the element should be inserted.\n * @returns {Number}\n */\nlunr.Vector.prototype.positionForIndex = function (index) {\n  // For an empty vector the tuple can be inserted at the beginning\n  if (this.elements.length == 0) {\n    return 0\n  }\n\n  var start = 0,\n      end = this.elements.length / 2,\n      sliceLength = end - start,\n      pivotPoint = Math.floor(sliceLength / 2),\n      pivotIndex = this.elements[pivotPoint * 2]\n\n  while (sliceLength > 1) {\n    if (pivotIndex < index) {\n      start = pivotPoint\n    }\n\n    if (pivotIndex > index) {\n      end = pivotPoint\n    }\n\n    if (pivotIndex == index) {\n      break\n    }\n\n    sliceLength = end - start\n    pivotPoint = start + Math.floor(sliceLength / 2)\n    pivotIndex = this.elements[pivotPoint * 2]\n  }\n\n  if (pivotIndex == index) {\n    return pivotPoint * 2\n  }\n\n  if (pivotIndex > index) {\n    return pivotPoint * 2\n  }\n\n  if (pivotIndex < index) {\n    return (pivotPoint + 1) * 2\n  }\n}\n\n/**\n * Inserts an element at an index within the vector.\n *\n * Does not allow duplicates, will throw an error if there is already an entry\n * for this index.\n *\n * @param {Number} insertIdx - The index at which the element should be inserted.\n * @param {Number} val - The value to be inserted into the vector.\n */\nlunr.Vector.prototype.insert = function (insertIdx, val) {\n  this.upsert(insertIdx, val, function () {\n    throw \"duplicate index\"\n  })\n}\n\n/**\n * Inserts or updates an existing index within the vector.\n *\n * @param {Number} insertIdx - The index at which the element should be inserted.\n * @param {Number} val - The value to be inserted into the vector.\n * @param {function} fn - A function that is called for updates, the existing value and the\n * requested value are passed as arguments\n */\nlunr.Vector.prototype.upsert = function (insertIdx, val, fn) {\n  this._magnitude = 0\n  var position = this.positionForIndex(insertIdx)\n\n  if (this.elements[position] == insertIdx) {\n    this.elements[position + 1] = fn(this.elements[position + 1], val)\n  } else {\n    this.elements.splice(position, 0, insertIdx, val)\n  }\n}\n\n/**\n * Calculates the magnitude of this vector.\n *\n * @returns {Number}\n */\nlunr.Vector.prototype.magnitude = function () {\n  if (this._magnitude) return this._magnitude\n\n  var sumOfSquares = 0,\n      elementsLength = this.elements.length\n\n  for (var i = 1; i < elementsLength; i += 2) {\n    var val = this.elements[i]\n    sumOfSquares += val * val\n  }\n\n  return this._magnitude = Math.sqrt(sumOfSquares)\n}\n\n/**\n * Calculates the dot product of this vector and another vector.\n *\n * @param {lunr.Vector} otherVector - The vector to compute the dot product with.\n * @returns {Number}\n */\nlunr.Vector.prototype.dot = function (otherVector) {\n  var dotProduct = 0,\n      a = this.elements, b = otherVector.elements,\n      aLen = a.length, bLen = b.length,\n      aVal = 0, bVal = 0,\n      i = 0, j = 0\n\n  while (i < aLen && j < bLen) {\n    aVal = a[i], bVal = b[j]\n    if (aVal < bVal) {\n      i += 2\n    } else if (aVal > bVal) {\n      j += 2\n    } else if (aVal == bVal) {\n      dotProduct += a[i + 1] * b[j + 1]\n      i += 2\n      j += 2\n    }\n  }\n\n  return dotProduct\n}\n\n/**\n * Calculates the similarity between this vector and another vector.\n *\n * @param {lunr.Vector} otherVector - The other vector to calculate the\n * similarity with.\n * @returns {Number}\n */\nlunr.Vector.prototype.similarity = function (otherVector) {\n  return this.dot(otherVector) / this.magnitude() || 0\n}\n\n/**\n * Converts the vector to an array of the elements within the vector.\n *\n * @returns {Number[]}\n */\nlunr.Vector.prototype.toArray = function () {\n  var output = new Array (this.elements.length / 2)\n\n  for (var i = 1, j = 0; i < this.elements.length; i += 2, j++) {\n    output[j] = this.elements[i]\n  }\n\n  return output\n}\n\n/**\n * A JSON serializable representation of the vector.\n *\n * @returns {Number[]}\n */\nlunr.Vector.prototype.toJSON = function () {\n  return this.elements\n}\n/* eslint-disable */\n/*!\n * lunr.stemmer\n * Copyright (C) 2020 Oliver Nightingale\n * Includes code from - http://tartarus.org/~martin/PorterStemmer/js.txt\n */\n\n/**\n * lunr.stemmer is an english language stemmer, this is a JavaScript\n * implementation of the PorterStemmer taken from http://tartarus.org/~martin\n *\n * @static\n * @implements {lunr.PipelineFunction}\n * @param {lunr.Token} token - The string to stem\n * @returns {lunr.Token}\n * @see {@link lunr.Pipeline}\n * @function\n */\nlunr.stemmer = (function(){\n  var step2list = {\n      \"ational\" : \"ate\",\n      \"tional\" : \"tion\",\n      \"enci\" : \"ence\",\n      \"anci\" : \"ance\",\n      \"izer\" : \"ize\",\n      \"bli\" : \"ble\",\n      \"alli\" : \"al\",\n      \"entli\" : \"ent\",\n      \"eli\" : \"e\",\n      \"ousli\" : \"ous\",\n      \"ization\" : \"ize\",\n      \"ation\" : \"ate\",\n      \"ator\" : \"ate\",\n      \"alism\" : \"al\",\n      \"iveness\" : \"ive\",\n      \"fulness\" : \"ful\",\n      \"ousness\" : \"ous\",\n      \"aliti\" : \"al\",\n      \"iviti\" : \"ive\",\n      \"biliti\" : \"ble\",\n      \"logi\" : \"log\"\n    },\n\n    step3list = {\n      \"icate\" : \"ic\",\n      \"ative\" : \"\",\n      \"alize\" : \"al\",\n      \"iciti\" : \"ic\",\n      \"ical\" : \"ic\",\n      \"ful\" : \"\",\n      \"ness\" : \"\"\n    },\n\n    c = \"[^aeiou]\",          // consonant\n    v = \"[aeiouy]\",          // vowel\n    C = c + \"[^aeiouy]*\",    // consonant sequence\n    V = v + \"[aeiou]*\",      // vowel sequence\n\n    mgr0 = \"^(\" + C + \")?\" + V + C,               // [C]VC... is m>0\n    meq1 = \"^(\" + C + \")?\" + V + C + \"(\" + V + \")?$\",  // [C]VC[V] is m=1\n    mgr1 = \"^(\" + C + \")?\" + V + C + V + C,       // [C]VCVC... is m>1\n    s_v = \"^(\" + C + \")?\" + v;                   // vowel in stem\n\n  var re_mgr0 = new RegExp(mgr0);\n  var re_mgr1 = new RegExp(mgr1);\n  var re_meq1 = new RegExp(meq1);\n  var re_s_v = new RegExp(s_v);\n\n  var re_1a = /^(.+?)(ss|i)es$/;\n  var re2_1a = /^(.+?)([^s])s$/;\n  var re_1b = /^(.+?)eed$/;\n  var re2_1b = /^(.+?)(ed|ing)$/;\n  var re_1b_2 = /.$/;\n  var re2_1b_2 = /(at|bl|iz)$/;\n  var re3_1b_2 = new RegExp(\"([^aeiouylsz])\\\\1$\");\n  var re4_1b_2 = new RegExp(\"^\" + C + v + \"[^aeiouwxy]$\");\n\n  var re_1c = /^(.+?[^aeiou])y$/;\n  var re_2 = /^(.+?)(ational|tional|enci|anci|izer|bli|alli|entli|eli|ousli|ization|ation|ator|alism|iveness|fulness|ousness|aliti|iviti|biliti|logi)$/;\n\n  var re_3 = /^(.+?)(icate|ative|alize|iciti|ical|ful|ness)$/;\n\n  var re_4 = /^(.+?)(al|ance|ence|er|ic|able|ible|ant|ement|ment|ent|ou|ism|ate|iti|ous|ive|ize)$/;\n  var re2_4 = /^(.+?)(s|t)(ion)$/;\n\n  var re_5 = /^(.+?)e$/;\n  var re_5_1 = /ll$/;\n  var re3_5 = new RegExp(\"^\" + C + v + \"[^aeiouwxy]$\");\n\n  var porterStemmer = function porterStemmer(w) {\n    var stem,\n      suffix,\n      firstch,\n      re,\n      re2,\n      re3,\n      re4;\n\n    if (w.length < 3) { return w; }\n\n    firstch = w.substr(0,1);\n    if (firstch == \"y\") {\n      w = firstch.toUpperCase() + w.substr(1);\n    }\n\n    // Step 1a\n    re = re_1a\n    re2 = re2_1a;\n\n    if (re.test(w)) { w = w.replace(re,\"$1$2\"); }\n    else if (re2.test(w)) { w = w.replace(re2,\"$1$2\"); }\n\n    // Step 1b\n    re = re_1b;\n    re2 = re2_1b;\n    if (re.test(w)) {\n      var fp = re.exec(w);\n      re = re_mgr0;\n      if (re.test(fp[1])) {\n        re = re_1b_2;\n        w = w.replace(re,\"\");\n      }\n    } else if (re2.test(w)) {\n      var fp = re2.exec(w);\n      stem = fp[1];\n      re2 = re_s_v;\n      if (re2.test(stem)) {\n        w = stem;\n        re2 = re2_1b_2;\n        re3 = re3_1b_2;\n        re4 = re4_1b_2;\n        if (re2.test(w)) { w = w + \"e\"; }\n        else if (re3.test(w)) { re = re_1b_2; w = w.replace(re,\"\"); }\n        else if (re4.test(w)) { w = w + \"e\"; }\n      }\n    }\n\n    // Step 1c - replace suffix y or Y by i if preceded by a non-vowel which is not the first letter of the word (so cry -> cri, by -> by, say -> say)\n    re = re_1c;\n    if (re.test(w)) {\n      var fp = re.exec(w);\n      stem = fp[1];\n      w = stem + \"i\";\n    }\n\n    // Step 2\n    re = re_2;\n    if (re.test(w)) {\n      var fp = re.exec(w);\n      stem = fp[1];\n      suffix = fp[2];\n      re = re_mgr0;\n      if (re.test(stem)) {\n        w = stem + step2list[suffix];\n      }\n    }\n\n    // Step 3\n    re = re_3;\n    if (re.test(w)) {\n      var fp = re.exec(w);\n      stem = fp[1];\n      suffix = fp[2];\n      re = re_mgr0;\n      if (re.test(stem)) {\n        w = stem + step3list[suffix];\n      }\n    }\n\n    // Step 4\n    re = re_4;\n    re2 = re2_4;\n    if (re.test(w)) {\n      var fp = re.exec(w);\n      stem = fp[1];\n      re = re_mgr1;\n      if (re.test(stem)) {\n        w = stem;\n      }\n    } else if (re2.test(w)) {\n      var fp = re2.exec(w);\n      stem = fp[1] + fp[2];\n      re2 = re_mgr1;\n      if (re2.test(stem)) {\n        w = stem;\n      }\n    }\n\n    // Step 5\n    re = re_5;\n    if (re.test(w)) {\n      var fp = re.exec(w);\n      stem = fp[1];\n      re = re_mgr1;\n      re2 = re_meq1;\n      re3 = re3_5;\n      if (re.test(stem) || (re2.test(stem) && !(re3.test(stem)))) {\n        w = stem;\n      }\n    }\n\n    re = re_5_1;\n    re2 = re_mgr1;\n    if (re.test(w) && re2.test(w)) {\n      re = re_1b_2;\n      w = w.replace(re,\"\");\n    }\n\n    // and turn initial Y back to y\n\n    if (firstch == \"y\") {\n      w = firstch.toLowerCase() + w.substr(1);\n    }\n\n    return w;\n  };\n\n  return function (token) {\n    return token.update(porterStemmer);\n  }\n})();\n\nlunr.Pipeline.registerFunction(lunr.stemmer, 'stemmer')\n/*!\n * lunr.stopWordFilter\n * Copyright (C) 2020 Oliver Nightingale\n */\n\n/**\n * lunr.generateStopWordFilter builds a stopWordFilter function from the provided\n * list of stop words.\n *\n * The built in lunr.stopWordFilter is built using this generator and can be used\n * to generate custom stopWordFilters for applications or non English languages.\n *\n * @function\n * @param {Array} token The token to pass through the filter\n * @returns {lunr.PipelineFunction}\n * @see lunr.Pipeline\n * @see lunr.stopWordFilter\n */\nlunr.generateStopWordFilter = function (stopWords) {\n  var words = stopWords.reduce(function (memo, stopWord) {\n    memo[stopWord] = stopWord\n    return memo\n  }, {})\n\n  return function (token) {\n    if (token && words[token.toString()] !== token.toString()) return token\n  }\n}\n\n/**\n * lunr.stopWordFilter is an English language stop word list filter, any words\n * contained in the list will not be passed through the filter.\n *\n * This is intended to be used in the Pipeline. If the token does not pass the\n * filter then undefined will be returned.\n *\n * @function\n * @implements {lunr.PipelineFunction}\n * @params {lunr.Token} token - A token to check for being a stop word.\n * @returns {lunr.Token}\n * @see {@link lunr.Pipeline}\n */\nlunr.stopWordFilter = lunr.generateStopWordFilter([\n  'a',\n  'able',\n  'about',\n  'across',\n  'after',\n  'all',\n  'almost',\n  'also',\n  'am',\n  'among',\n  'an',\n  'and',\n  'any',\n  'are',\n  'as',\n  'at',\n  'be',\n  'because',\n  'been',\n  'but',\n  'by',\n  'can',\n  'cannot',\n  'could',\n  'dear',\n  'did',\n  'do',\n  'does',\n  'either',\n  'else',\n  'ever',\n  'every',\n  'for',\n  'from',\n  'get',\n  'got',\n  'had',\n  'has',\n  'have',\n  'he',\n  'her',\n  'hers',\n  'him',\n  'his',\n  'how',\n  'however',\n  'i',\n  'if',\n  'in',\n  'into',\n  'is',\n  'it',\n  'its',\n  'just',\n  'least',\n  'let',\n  'like',\n  'likely',\n  'may',\n  'me',\n  'might',\n  'most',\n  'must',\n  'my',\n  'neither',\n  'no',\n  'nor',\n  'not',\n  'of',\n  'off',\n  'often',\n  'on',\n  'only',\n  'or',\n  'other',\n  'our',\n  'own',\n  'rather',\n  'said',\n  'say',\n  'says',\n  'she',\n  'should',\n  'since',\n  'so',\n  'some',\n  'than',\n  'that',\n  'the',\n  'their',\n  'them',\n  'then',\n  'there',\n  'these',\n  'they',\n  'this',\n  'tis',\n  'to',\n  'too',\n  'twas',\n  'us',\n  'wants',\n  'was',\n  'we',\n  'were',\n  'what',\n  'when',\n  'where',\n  'which',\n  'while',\n  'who',\n  'whom',\n  'why',\n  'will',\n  'with',\n  'would',\n  'yet',\n  'you',\n  'your'\n])\n\nlunr.Pipeline.registerFunction(lunr.stopWordFilter, 'stopWordFilter')\n/*!\n * lunr.trimmer\n * Copyright (C) 2020 Oliver Nightingale\n */\n\n/**\n * lunr.trimmer is a pipeline function for trimming non word\n * characters from the beginning and end of tokens before they\n * enter the index.\n *\n * This implementation may not work correctly for non latin\n * characters and should either be removed or adapted for use\n * with languages with non-latin characters.\n *\n * @static\n * @implements {lunr.PipelineFunction}\n * @param {lunr.Token} token The token to pass through the filter\n * @returns {lunr.Token}\n * @see lunr.Pipeline\n */\nlunr.trimmer = function (token) {\n  return token.update(function (s) {\n    return s.replace(/^\\W+/, '').replace(/\\W+$/, '')\n  })\n}\n\nlunr.Pipeline.registerFunction(lunr.trimmer, 'trimmer')\n/*!\n * lunr.TokenSet\n * Copyright (C) 2020 Oliver Nightingale\n */\n\n/**\n * A token set is used to store the unique list of all tokens\n * within an index. Token sets are also used to represent an\n * incoming query to the index, this query token set and index\n * token set are then intersected to find which tokens to look\n * up in the inverted index.\n *\n * A token set can hold multiple tokens, as in the case of the\n * index token set, or it can hold a single token as in the\n * case of a simple query token set.\n *\n * Additionally token sets are used to perform wildcard matching.\n * Leading, contained and trailing wildcards are supported, and\n * from this edit distance matching can also be provided.\n *\n * Token sets are implemented as a minimal finite state automata,\n * where both common prefixes and suffixes are shared between tokens.\n * This helps to reduce the space used for storing the token set.\n *\n * @constructor\n */\nlunr.TokenSet = function () {\n  this.final = false\n  this.edges = {}\n  this.id = lunr.TokenSet._nextId\n  lunr.TokenSet._nextId += 1\n}\n\n/**\n * Keeps track of the next, auto increment, identifier to assign\n * to a new tokenSet.\n *\n * TokenSets require a unique identifier to be correctly minimised.\n *\n * @private\n */\nlunr.TokenSet._nextId = 1\n\n/**\n * Creates a TokenSet instance from the given sorted array of words.\n *\n * @param {String[]} arr - A sorted array of strings to create the set from.\n * @returns {lunr.TokenSet}\n * @throws Will throw an error if the input array is not sorted.\n */\nlunr.TokenSet.fromArray = function (arr) {\n  var builder = new lunr.TokenSet.Builder\n\n  for (var i = 0, len = arr.length; i < len; i++) {\n    builder.insert(arr[i])\n  }\n\n  builder.finish()\n  return builder.root\n}\n\n/**\n * Creates a token set from a query clause.\n *\n * @private\n * @param {Object} clause - A single clause from lunr.Query.\n * @param {string} clause.term - The query clause term.\n * @param {number} [clause.editDistance] - The optional edit distance for the term.\n * @returns {lunr.TokenSet}\n */\nlunr.TokenSet.fromClause = function (clause) {\n  if ('editDistance' in clause) {\n    return lunr.TokenSet.fromFuzzyString(clause.term, clause.editDistance)\n  } else {\n    return lunr.TokenSet.fromString(clause.term)\n  }\n}\n\n/**\n * Creates a token set representing a single string with a specified\n * edit distance.\n *\n * Insertions, deletions, substitutions and transpositions are each\n * treated as an edit distance of 1.\n *\n * Increasing the allowed edit distance will have a dramatic impact\n * on the performance of both creating and intersecting these TokenSets.\n * It is advised to keep the edit distance less than 3.\n *\n * @param {string} str - The string to create the token set from.\n * @param {number} editDistance - The allowed edit distance to match.\n * @returns {lunr.Vector}\n */\nlunr.TokenSet.fromFuzzyString = function (str, editDistance) {\n  var root = new lunr.TokenSet\n\n  var stack = [{\n    node: root,\n    editsRemaining: editDistance,\n    str: str\n  }]\n\n  while (stack.length) {\n    var frame = stack.pop()\n\n    // no edit\n    if (frame.str.length > 0) {\n      var char = frame.str.charAt(0),\n          noEditNode\n\n      if (char in frame.node.edges) {\n        noEditNode = frame.node.edges[char]\n      } else {\n        noEditNode = new lunr.TokenSet\n        frame.node.edges[char] = noEditNode\n      }\n\n      if (frame.str.length == 1) {\n        noEditNode.final = true\n      }\n\n      stack.push({\n        node: noEditNode,\n        editsRemaining: frame.editsRemaining,\n        str: frame.str.slice(1)\n      })\n    }\n\n    if (frame.editsRemaining == 0) {\n      continue\n    }\n\n    // insertion\n    if (\"*\" in frame.node.edges) {\n      var insertionNode = frame.node.edges[\"*\"]\n    } else {\n      var insertionNode = new lunr.TokenSet\n      frame.node.edges[\"*\"] = insertionNode\n    }\n\n    if (frame.str.length == 0) {\n      insertionNode.final = true\n    }\n\n    stack.push({\n      node: insertionNode,\n      editsRemaining: frame.editsRemaining - 1,\n      str: frame.str\n    })\n\n    // deletion\n    // can only do a deletion if we have enough edits remaining\n    // and if there are characters left to delete in the string\n    if (frame.str.length > 1) {\n      stack.push({\n        node: frame.node,\n        editsRemaining: frame.editsRemaining - 1,\n        str: frame.str.slice(1)\n      })\n    }\n\n    // deletion\n    // just removing the last character from the str\n    if (frame.str.length == 1) {\n      frame.node.final = true\n    }\n\n    // substitution\n    // can only do a substitution if we have enough edits remaining\n    // and if there are characters left to substitute\n    if (frame.str.length >= 1) {\n      if (\"*\" in frame.node.edges) {\n        var substitutionNode = frame.node.edges[\"*\"]\n      } else {\n        var substitutionNode = new lunr.TokenSet\n        frame.node.edges[\"*\"] = substitutionNode\n      }\n\n      if (frame.str.length == 1) {\n        substitutionNode.final = true\n      }\n\n      stack.push({\n        node: substitutionNode,\n        editsRemaining: frame.editsRemaining - 1,\n        str: frame.str.slice(1)\n      })\n    }\n\n    // transposition\n    // can only do a transposition if there are edits remaining\n    // and there are enough characters to transpose\n    if (frame.str.length > 1) {\n      var charA = frame.str.charAt(0),\n          charB = frame.str.charAt(1),\n          transposeNode\n\n      if (charB in frame.node.edges) {\n        transposeNode = frame.node.edges[charB]\n      } else {\n        transposeNode = new lunr.TokenSet\n        frame.node.edges[charB] = transposeNode\n      }\n\n      if (frame.str.length == 1) {\n        transposeNode.final = true\n      }\n\n      stack.push({\n        node: transposeNode,\n        editsRemaining: frame.editsRemaining - 1,\n        str: charA + frame.str.slice(2)\n      })\n    }\n  }\n\n  return root\n}\n\n/**\n * Creates a TokenSet from a string.\n *\n * The string may contain one or more wildcard characters (*)\n * that will allow wildcard matching when intersecting with\n * another TokenSet.\n *\n * @param {string} str - The string to create a TokenSet from.\n * @returns {lunr.TokenSet}\n */\nlunr.TokenSet.fromString = function (str) {\n  var node = new lunr.TokenSet,\n      root = node\n\n  /*\n   * Iterates through all characters within the passed string\n   * appending a node for each character.\n   *\n   * When a wildcard character is found then a self\n   * referencing edge is introduced to continually match\n   * any number of any characters.\n   */\n  for (var i = 0, len = str.length; i < len; i++) {\n    var char = str[i],\n        final = (i == len - 1)\n\n    if (char == \"*\") {\n      node.edges[char] = node\n      node.final = final\n\n    } else {\n      var next = new lunr.TokenSet\n      next.final = final\n\n      node.edges[char] = next\n      node = next\n    }\n  }\n\n  return root\n}\n\n/**\n * Converts this TokenSet into an array of strings\n * contained within the TokenSet.\n *\n * This is not intended to be used on a TokenSet that\n * contains wildcards, in these cases the results are\n * undefined and are likely to cause an infinite loop.\n *\n * @returns {string[]}\n */\nlunr.TokenSet.prototype.toArray = function () {\n  var words = []\n\n  var stack = [{\n    prefix: \"\",\n    node: this\n  }]\n\n  while (stack.length) {\n    var frame = stack.pop(),\n        edges = Object.keys(frame.node.edges),\n        len = edges.length\n\n    if (frame.node.final) {\n      /* In Safari, at this point the prefix is sometimes corrupted, see:\n       * https://github.com/olivernn/lunr.js/issues/279 Calling any\n       * String.prototype method forces Safari to \"cast\" this string to what\n       * it's supposed to be, fixing the bug. */\n      frame.prefix.charAt(0)\n      words.push(frame.prefix)\n    }\n\n    for (var i = 0; i < len; i++) {\n      var edge = edges[i]\n\n      stack.push({\n        prefix: frame.prefix.concat(edge),\n        node: frame.node.edges[edge]\n      })\n    }\n  }\n\n  return words\n}\n\n/**\n * Generates a string representation of a TokenSet.\n *\n * This is intended to allow TokenSets to be used as keys\n * in objects, largely to aid the construction and minimisation\n * of a TokenSet. As such it is not designed to be a human\n * friendly representation of the TokenSet.\n *\n * @returns {string}\n */\nlunr.TokenSet.prototype.toString = function () {\n  // NOTE: Using Object.keys here as this.edges is very likely\n  // to enter 'hash-mode' with many keys being added\n  //\n  // avoiding a for-in loop here as it leads to the function\n  // being de-optimised (at least in V8). From some simple\n  // benchmarks the performance is comparable, but allowing\n  // V8 to optimize may mean easy performance wins in the future.\n\n  if (this._str) {\n    return this._str\n  }\n\n  var str = this.final ? '1' : '0',\n      labels = Object.keys(this.edges).sort(),\n      len = labels.length\n\n  for (var i = 0; i < len; i++) {\n    var label = labels[i],\n        node = this.edges[label]\n\n    str = str + label + node.id\n  }\n\n  return str\n}\n\n/**\n * Returns a new TokenSet that is the intersection of\n * this TokenSet and the passed TokenSet.\n *\n * This intersection will take into account any wildcards\n * contained within the TokenSet.\n *\n * @param {lunr.TokenSet} b - An other TokenSet to intersect with.\n * @returns {lunr.TokenSet}\n */\nlunr.TokenSet.prototype.intersect = function (b) {\n  var output = new lunr.TokenSet,\n      frame = undefined\n\n  var stack = [{\n    qNode: b,\n    output: output,\n    node: this\n  }]\n\n  while (stack.length) {\n    frame = stack.pop()\n\n    // NOTE: As with the #toString method, we are using\n    // Object.keys and a for loop instead of a for-in loop\n    // as both of these objects enter 'hash' mode, causing\n    // the function to be de-optimised in V8\n    var qEdges = Object.keys(frame.qNode.edges),\n        qLen = qEdges.length,\n        nEdges = Object.keys(frame.node.edges),\n        nLen = nEdges.length\n\n    for (var q = 0; q < qLen; q++) {\n      var qEdge = qEdges[q]\n\n      for (var n = 0; n < nLen; n++) {\n        var nEdge = nEdges[n]\n\n        if (nEdge == qEdge || qEdge == '*') {\n          var node = frame.node.edges[nEdge],\n              qNode = frame.qNode.edges[qEdge],\n              final = node.final && qNode.final,\n              next = undefined\n\n          if (nEdge in frame.output.edges) {\n            // an edge already exists for this character\n            // no need to create a new node, just set the finality\n            // bit unless this node is already final\n            next = frame.output.edges[nEdge]\n            next.final = next.final || final\n\n          } else {\n            // no edge exists yet, must create one\n            // set the finality bit and insert it\n            // into the output\n            next = new lunr.TokenSet\n            next.final = final\n            frame.output.edges[nEdge] = next\n          }\n\n          stack.push({\n            qNode: qNode,\n            output: next,\n            node: node\n          })\n        }\n      }\n    }\n  }\n\n  return output\n}\nlunr.TokenSet.Builder = function () {\n  this.previousWord = \"\"\n  this.root = new lunr.TokenSet\n  this.uncheckedNodes = []\n  this.minimizedNodes = {}\n}\n\nlunr.TokenSet.Builder.prototype.insert = function (word) {\n  var node,\n      commonPrefix = 0\n\n  if (word < this.previousWord) {\n    throw new Error (\"Out of order word insertion\")\n  }\n\n  for (var i = 0; i < word.length && i < this.previousWord.length; i++) {\n    if (word[i] != this.previousWord[i]) break\n    commonPrefix++\n  }\n\n  this.minimize(commonPrefix)\n\n  if (this.uncheckedNodes.length == 0) {\n    node = this.root\n  } else {\n    node = this.uncheckedNodes[this.uncheckedNodes.length - 1].child\n  }\n\n  for (var i = commonPrefix; i < word.length; i++) {\n    var nextNode = new lunr.TokenSet,\n        char = word[i]\n\n    node.edges[char] = nextNode\n\n    this.uncheckedNodes.push({\n      parent: node,\n      char: char,\n      child: nextNode\n    })\n\n    node = nextNode\n  }\n\n  node.final = true\n  this.previousWord = word\n}\n\nlunr.TokenSet.Builder.prototype.finish = function () {\n  this.minimize(0)\n}\n\nlunr.TokenSet.Builder.prototype.minimize = function (downTo) {\n  for (var i = this.uncheckedNodes.length - 1; i >= downTo; i--) {\n    var node = this.uncheckedNodes[i],\n        childKey = node.child.toString()\n\n    if (childKey in this.minimizedNodes) {\n      node.parent.edges[node.char] = this.minimizedNodes[childKey]\n    } else {\n      // Cache the key for this node since\n      // we know it can't change anymore\n      node.child._str = childKey\n\n      this.minimizedNodes[childKey] = node.child\n    }\n\n    this.uncheckedNodes.pop()\n  }\n}\n/*!\n * lunr.Index\n * Copyright (C) 2020 Oliver Nightingale\n */\n\n/**\n * An index contains the built index of all documents and provides a query interface\n * to the index.\n *\n * Usually instances of lunr.Index will not be created using this constructor, instead\n * lunr.Builder should be used to construct new indexes, or lunr.Index.load should be\n * used to load previously built and serialized indexes.\n *\n * @constructor\n * @param {Object} attrs - The attributes of the built search index.\n * @param {Object} attrs.invertedIndex - An index of term/field to document reference.\n * @param {Object<string, lunr.Vector>} attrs.fieldVectors - Field vectors\n * @param {lunr.TokenSet} attrs.tokenSet - An set of all corpus tokens.\n * @param {string[]} attrs.fields - The names of indexed document fields.\n * @param {lunr.Pipeline} attrs.pipeline - The pipeline to use for search terms.\n */\nlunr.Index = function (attrs) {\n  this.invertedIndex = attrs.invertedIndex\n  this.fieldVectors = attrs.fieldVectors\n  this.tokenSet = attrs.tokenSet\n  this.fields = attrs.fields\n  this.pipeline = attrs.pipeline\n}\n\n/**\n * A result contains details of a document matching a search query.\n * @typedef {Object} lunr.Index~Result\n * @property {string} ref - The reference of the document this result represents.\n * @property {number} score - A number between 0 and 1 representing how similar this document is to the query.\n * @property {lunr.MatchData} matchData - Contains metadata about this match including which term(s) caused the match.\n */\n\n/**\n * Although lunr provides the ability to create queries using lunr.Query, it also provides a simple\n * query language which itself is parsed into an instance of lunr.Query.\n *\n * For programmatically building queries it is advised to directly use lunr.Query, the query language\n * is best used for human entered text rather than program generated text.\n *\n * At its simplest queries can just be a single term, e.g. `hello`, multiple terms are also supported\n * and will be combined with OR, e.g `hello world` will match documents that contain either 'hello'\n * or 'world', though those that contain both will rank higher in the results.\n *\n * Wildcards can be included in terms to match one or more unspecified characters, these wildcards can\n * be inserted anywhere within the term, and more than one wildcard can exist in a single term. Adding\n * wildcards will increase the number of documents that will be found but can also have a negative\n * impact on query performance, especially with wildcards at the beginning of a term.\n *\n * Terms can be restricted to specific fields, e.g. `title:hello`, only documents with the term\n * hello in the title field will match this query. Using a field not present in the index will lead\n * to an error being thrown.\n *\n * Modifiers can also be added to terms, lunr supports edit distance and boost modifiers on terms. A term\n * boost will make documents matching that term score higher, e.g. `foo^5`. Edit distance is also supported\n * to provide fuzzy matching, e.g. 'hello~2' will match documents with hello with an edit distance of 2.\n * Avoid large values for edit distance to improve query performance.\n *\n * Each term also supports a presence modifier. By default a term's presence in document is optional, however\n * this can be changed to either required or prohibited. For a term's presence to be required in a document the\n * term should be prefixed with a '+', e.g. `+foo bar` is a search for documents that must contain 'foo' and\n * optionally contain 'bar'. Conversely a leading '-' sets the terms presence to prohibited, i.e. it must not\n * appear in a document, e.g. `-foo bar` is a search for documents that do not contain 'foo' but may contain 'bar'.\n *\n * To escape special characters the backslash character '\\' can be used, this allows searches to include\n * characters that would normally be considered modifiers, e.g. `foo\\~2` will search for a term \"foo~2\" instead\n * of attempting to apply a boost of 2 to the search term \"foo\".\n *\n * @typedef {string} lunr.Index~QueryString\n * @example <caption>Simple single term query</caption>\n * hello\n * @example <caption>Multiple term query</caption>\n * hello world\n * @example <caption>term scoped to a field</caption>\n * title:hello\n * @example <caption>term with a boost of 10</caption>\n * hello^10\n * @example <caption>term with an edit distance of 2</caption>\n * hello~2\n * @example <caption>terms with presence modifiers</caption>\n * -foo +bar baz\n */\n\n/**\n * Performs a search against the index using lunr query syntax.\n *\n * Results will be returned sorted by their score, the most relevant results\n * will be returned first.  For details on how the score is calculated, please see\n * the {@link https://lunrjs.com/guides/searching.html#scoring|guide}.\n *\n * For more programmatic querying use lunr.Index#query.\n *\n * @param {lunr.Index~QueryString} queryString - A string containing a lunr query.\n * @throws {lunr.QueryParseError} If the passed query string cannot be parsed.\n * @returns {lunr.Index~Result[]}\n */\nlunr.Index.prototype.search = function (queryString) {\n  return this.query(function (query) {\n    var parser = new lunr.QueryParser(queryString, query)\n    parser.parse()\n  })\n}\n\n/**\n * A query builder callback provides a query object to be used to express\n * the query to perform on the index.\n *\n * @callback lunr.Index~queryBuilder\n * @param {lunr.Query} query - The query object to build up.\n * @this lunr.Query\n */\n\n/**\n * Performs a query against the index using the yielded lunr.Query object.\n *\n * If performing programmatic queries against the index, this method is preferred\n * over lunr.Index#search so as to avoid the additional query parsing overhead.\n *\n * A query object is yielded to the supplied function which should be used to\n * express the query to be run against the index.\n *\n * Note that although this function takes a callback parameter it is _not_ an\n * asynchronous operation, the callback is just yielded a query object to be\n * customized.\n *\n * @param {lunr.Index~queryBuilder} fn - A function that is used to build the query.\n * @returns {lunr.Index~Result[]}\n */\nlunr.Index.prototype.query = function (fn) {\n  // for each query clause\n  // * process terms\n  // * expand terms from token set\n  // * find matching documents and metadata\n  // * get document vectors\n  // * score documents\n\n  var query = new lunr.Query(this.fields),\n      matchingFields = Object.create(null),\n      queryVectors = Object.create(null),\n      termFieldCache = Object.create(null),\n      requiredMatches = Object.create(null),\n      prohibitedMatches = Object.create(null)\n\n  /*\n   * To support field level boosts a query vector is created per\n   * field. An empty vector is eagerly created to support negated\n   * queries.\n   */\n  for (var i = 0; i < this.fields.length; i++) {\n    queryVectors[this.fields[i]] = new lunr.Vector\n  }\n\n  fn.call(query, query)\n\n  for (var i = 0; i < query.clauses.length; i++) {\n    /*\n     * Unless the pipeline has been disabled for this term, which is\n     * the case for terms with wildcards, we need to pass the clause\n     * term through the search pipeline. A pipeline returns an array\n     * of processed terms. Pipeline functions may expand the passed\n     * term, which means we may end up performing multiple index lookups\n     * for a single query term.\n     */\n    var clause = query.clauses[i],\n        terms = null,\n        clauseMatches = lunr.Set.empty\n\n    if (clause.usePipeline) {\n      terms = this.pipeline.runString(clause.term, {\n        fields: clause.fields\n      })\n    } else {\n      terms = [clause.term]\n    }\n\n    for (var m = 0; m < terms.length; m++) {\n      var term = terms[m]\n\n      /*\n       * Each term returned from the pipeline needs to use the same query\n       * clause object, e.g. the same boost and or edit distance. The\n       * simplest way to do this is to re-use the clause object but mutate\n       * its term property.\n       */\n      clause.term = term\n\n      /*\n       * From the term in the clause we create a token set which will then\n       * be used to intersect the indexes token set to get a list of terms\n       * to lookup in the inverted index\n       */\n      var termTokenSet = lunr.TokenSet.fromClause(clause),\n          expandedTerms = this.tokenSet.intersect(termTokenSet).toArray()\n\n      /*\n       * If a term marked as required does not exist in the tokenSet it is\n       * impossible for the search to return any matches. We set all the field\n       * scoped required matches set to empty and stop examining any further\n       * clauses.\n       */\n      if (expandedTerms.length === 0 && clause.presence === lunr.Query.presence.REQUIRED) {\n        for (var k = 0; k < clause.fields.length; k++) {\n          var field = clause.fields[k]\n          requiredMatches[field] = lunr.Set.empty\n        }\n\n        break\n      }\n\n      for (var j = 0; j < expandedTerms.length; j++) {\n        /*\n         * For each term get the posting and termIndex, this is required for\n         * building the query vector.\n         */\n        var expandedTerm = expandedTerms[j],\n            posting = this.invertedIndex[expandedTerm],\n            termIndex = posting._index\n\n        for (var k = 0; k < clause.fields.length; k++) {\n          /*\n           * For each field that this query term is scoped by (by default\n           * all fields are in scope) we need to get all the document refs\n           * that have this term in that field.\n           *\n           * The posting is the entry in the invertedIndex for the matching\n           * term from above.\n           */\n          var field = clause.fields[k],\n              fieldPosting = posting[field],\n              matchingDocumentRefs = Object.keys(fieldPosting),\n              termField = expandedTerm + \"/\" + field,\n              matchingDocumentsSet = new lunr.Set(matchingDocumentRefs)\n\n          /*\n           * if the presence of this term is required ensure that the matching\n           * documents are added to the set of required matches for this clause.\n           *\n           */\n          if (clause.presence == lunr.Query.presence.REQUIRED) {\n            clauseMatches = clauseMatches.union(matchingDocumentsSet)\n\n            if (requiredMatches[field] === undefined) {\n              requiredMatches[field] = lunr.Set.complete\n            }\n          }\n\n          /*\n           * if the presence of this term is prohibited ensure that the matching\n           * documents are added to the set of prohibited matches for this field,\n           * creating that set if it does not yet exist.\n           */\n          if (clause.presence == lunr.Query.presence.PROHIBITED) {\n            if (prohibitedMatches[field] === undefined) {\n              prohibitedMatches[field] = lunr.Set.empty\n            }\n\n            prohibitedMatches[field] = prohibitedMatches[field].union(matchingDocumentsSet)\n\n            /*\n             * Prohibited matches should not be part of the query vector used for\n             * similarity scoring and no metadata should be extracted so we continue\n             * to the next field\n             */\n            continue\n          }\n\n          /*\n           * The query field vector is populated using the termIndex found for\n           * the term and a unit value with the appropriate boost applied.\n           * Using upsert because there could already be an entry in the vector\n           * for the term we are working with. In that case we just add the scores\n           * together.\n           */\n          queryVectors[field].upsert(termIndex, clause.boost, function (a, b) { return a + b })\n\n          /**\n           * If we've already seen this term, field combo then we've already collected\n           * the matching documents and metadata, no need to go through all that again\n           */\n          if (termFieldCache[termField]) {\n            continue\n          }\n\n          for (var l = 0; l < matchingDocumentRefs.length; l++) {\n            /*\n             * All metadata for this term/field/document triple\n             * are then extracted and collected into an instance\n             * of lunr.MatchData ready to be returned in the query\n             * results\n             */\n            var matchingDocumentRef = matchingDocumentRefs[l],\n                matchingFieldRef = new lunr.FieldRef (matchingDocumentRef, field),\n                metadata = fieldPosting[matchingDocumentRef],\n                fieldMatch\n\n            if ((fieldMatch = matchingFields[matchingFieldRef]) === undefined) {\n              matchingFields[matchingFieldRef] = new lunr.MatchData (expandedTerm, field, metadata)\n            } else {\n              fieldMatch.add(expandedTerm, field, metadata)\n            }\n\n          }\n\n          termFieldCache[termField] = true\n        }\n      }\n    }\n\n    /**\n     * If the presence was required we need to update the requiredMatches field sets.\n     * We do this after all fields for the term have collected their matches because\n     * the clause terms presence is required in _any_ of the fields not _all_ of the\n     * fields.\n     */\n    if (clause.presence === lunr.Query.presence.REQUIRED) {\n      for (var k = 0; k < clause.fields.length; k++) {\n        var field = clause.fields[k]\n        requiredMatches[field] = requiredMatches[field].intersect(clauseMatches)\n      }\n    }\n  }\n\n  /**\n   * Need to combine the field scoped required and prohibited\n   * matching documents into a global set of required and prohibited\n   * matches\n   */\n  var allRequiredMatches = lunr.Set.complete,\n      allProhibitedMatches = lunr.Set.empty\n\n  for (var i = 0; i < this.fields.length; i++) {\n    var field = this.fields[i]\n\n    if (requiredMatches[field]) {\n      allRequiredMatches = allRequiredMatches.intersect(requiredMatches[field])\n    }\n\n    if (prohibitedMatches[field]) {\n      allProhibitedMatches = allProhibitedMatches.union(prohibitedMatches[field])\n    }\n  }\n\n  var matchingFieldRefs = Object.keys(matchingFields),\n      results = [],\n      matches = Object.create(null)\n\n  /*\n   * If the query is negated (contains only prohibited terms)\n   * we need to get _all_ fieldRefs currently existing in the\n   * index. This is only done when we know that the query is\n   * entirely prohibited terms to avoid any cost of getting all\n   * fieldRefs unnecessarily.\n   *\n   * Additionally, blank MatchData must be created to correctly\n   * populate the results.\n   */\n  if (query.isNegated()) {\n    matchingFieldRefs = Object.keys(this.fieldVectors)\n\n    for (var i = 0; i < matchingFieldRefs.length; i++) {\n      var matchingFieldRef = matchingFieldRefs[i]\n      var fieldRef = lunr.FieldRef.fromString(matchingFieldRef)\n      matchingFields[matchingFieldRef] = new lunr.MatchData\n    }\n  }\n\n  for (var i = 0; i < matchingFieldRefs.length; i++) {\n    /*\n     * Currently we have document fields that match the query, but we\n     * need to return documents. The matchData and scores are combined\n     * from multiple fields belonging to the same document.\n     *\n     * Scores are calculated by field, using the query vectors created\n     * above, and combined into a final document score using addition.\n     */\n    var fieldRef = lunr.FieldRef.fromString(matchingFieldRefs[i]),\n        docRef = fieldRef.docRef\n\n    if (!allRequiredMatches.contains(docRef)) {\n      continue\n    }\n\n    if (allProhibitedMatches.contains(docRef)) {\n      continue\n    }\n\n    var fieldVector = this.fieldVectors[fieldRef],\n        score = queryVectors[fieldRef.fieldName].similarity(fieldVector),\n        docMatch\n\n    if ((docMatch = matches[docRef]) !== undefined) {\n      docMatch.score += score\n      docMatch.matchData.combine(matchingFields[fieldRef])\n    } else {\n      var match = {\n        ref: docRef,\n        score: score,\n        matchData: matchingFields[fieldRef]\n      }\n      matches[docRef] = match\n      results.push(match)\n    }\n  }\n\n  /*\n   * Sort the results objects by score, highest first.\n   */\n  return results.sort(function (a, b) {\n    return b.score - a.score\n  })\n}\n\n/**\n * Prepares the index for JSON serialization.\n *\n * The schema for this JSON blob will be described in a\n * separate JSON schema file.\n *\n * @returns {Object}\n */\nlunr.Index.prototype.toJSON = function () {\n  var invertedIndex = Object.keys(this.invertedIndex)\n    .sort()\n    .map(function (term) {\n      return [term, this.invertedIndex[term]]\n    }, this)\n\n  var fieldVectors = Object.keys(this.fieldVectors)\n    .map(function (ref) {\n      return [ref, this.fieldVectors[ref].toJSON()]\n    }, this)\n\n  return {\n    version: lunr.version,\n    fields: this.fields,\n    fieldVectors: fieldVectors,\n    invertedIndex: invertedIndex,\n    pipeline: this.pipeline.toJSON()\n  }\n}\n\n/**\n * Loads a previously serialized lunr.Index\n *\n * @param {Object} serializedIndex - A previously serialized lunr.Index\n * @returns {lunr.Index}\n */\nlunr.Index.load = function (serializedIndex) {\n  var attrs = {},\n      fieldVectors = {},\n      serializedVectors = serializedIndex.fieldVectors,\n      invertedIndex = Object.create(null),\n      serializedInvertedIndex = serializedIndex.invertedIndex,\n      tokenSetBuilder = new lunr.TokenSet.Builder,\n      pipeline = lunr.Pipeline.load(serializedIndex.pipeline)\n\n  if (serializedIndex.version != lunr.version) {\n    lunr.utils.warn(\"Version mismatch when loading serialised index. Current version of lunr '\" + lunr.version + \"' does not match serialized index '\" + serializedIndex.version + \"'\")\n  }\n\n  for (var i = 0; i < serializedVectors.length; i++) {\n    var tuple = serializedVectors[i],\n        ref = tuple[0],\n        elements = tuple[1]\n\n    fieldVectors[ref] = new lunr.Vector(elements)\n  }\n\n  for (var i = 0; i < serializedInvertedIndex.length; i++) {\n    var tuple = serializedInvertedIndex[i],\n        term = tuple[0],\n        posting = tuple[1]\n\n    tokenSetBuilder.insert(term)\n    invertedIndex[term] = posting\n  }\n\n  tokenSetBuilder.finish()\n\n  attrs.fields = serializedIndex.fields\n\n  attrs.fieldVectors = fieldVectors\n  attrs.invertedIndex = invertedIndex\n  attrs.tokenSet = tokenSetBuilder.root\n  attrs.pipeline = pipeline\n\n  return new lunr.Index(attrs)\n}\n/*!\n * lunr.Builder\n * Copyright (C) 2020 Oliver Nightingale\n */\n\n/**\n * lunr.Builder performs indexing on a set of documents and\n * returns instances of lunr.Index ready for querying.\n *\n * All configuration of the index is done via the builder, the\n * fields to index, the document reference, the text processing\n * pipeline and document scoring parameters are all set on the\n * builder before indexing.\n *\n * @constructor\n * @property {string} _ref - Internal reference to the document reference field.\n * @property {string[]} _fields - Internal reference to the document fields to index.\n * @property {object} invertedIndex - The inverted index maps terms to document fields.\n * @property {object} documentTermFrequencies - Keeps track of document term frequencies.\n * @property {object} documentLengths - Keeps track of the length of documents added to the index.\n * @property {lunr.tokenizer} tokenizer - Function for splitting strings into tokens for indexing.\n * @property {lunr.Pipeline} pipeline - The pipeline performs text processing on tokens before indexing.\n * @property {lunr.Pipeline} searchPipeline - A pipeline for processing search terms before querying the index.\n * @property {number} documentCount - Keeps track of the total number of documents indexed.\n * @property {number} _b - A parameter to control field length normalization, setting this to 0 disabled normalization, 1 fully normalizes field lengths, the default value is 0.75.\n * @property {number} _k1 - A parameter to control how quickly an increase in term frequency results in term frequency saturation, the default value is 1.2.\n * @property {number} termIndex - A counter incremented for each unique term, used to identify a terms position in the vector space.\n * @property {array} metadataWhitelist - A list of metadata keys that have been whitelisted for entry in the index.\n */\nlunr.Builder = function () {\n  this._ref = \"id\"\n  this._fields = Object.create(null)\n  this._documents = Object.create(null)\n  this.invertedIndex = Object.create(null)\n  this.fieldTermFrequencies = {}\n  this.fieldLengths = {}\n  this.tokenizer = lunr.tokenizer\n  this.pipeline = new lunr.Pipeline\n  this.searchPipeline = new lunr.Pipeline\n  this.documentCount = 0\n  this._b = 0.75\n  this._k1 = 1.2\n  this.termIndex = 0\n  this.metadataWhitelist = []\n}\n\n/**\n * Sets the document field used as the document reference. Every document must have this field.\n * The type of this field in the document should be a string, if it is not a string it will be\n * coerced into a string by calling toString.\n *\n * The default ref is 'id'.\n *\n * The ref should _not_ be changed during indexing, it should be set before any documents are\n * added to the index. Changing it during indexing can lead to inconsistent results.\n *\n * @param {string} ref - The name of the reference field in the document.\n */\nlunr.Builder.prototype.ref = function (ref) {\n  this._ref = ref\n}\n\n/**\n * A function that is used to extract a field from a document.\n *\n * Lunr expects a field to be at the top level of a document, if however the field\n * is deeply nested within a document an extractor function can be used to extract\n * the right field for indexing.\n *\n * @callback fieldExtractor\n * @param {object} doc - The document being added to the index.\n * @returns {?(string|object|object[])} obj - The object that will be indexed for this field.\n * @example <caption>Extracting a nested field</caption>\n * function (doc) { return doc.nested.field }\n */\n\n/**\n * Adds a field to the list of document fields that will be indexed. Every document being\n * indexed should have this field. Null values for this field in indexed documents will\n * not cause errors but will limit the chance of that document being retrieved by searches.\n *\n * All fields should be added before adding documents to the index. Adding fields after\n * a document has been indexed will have no effect on already indexed documents.\n *\n * Fields can be boosted at build time. This allows terms within that field to have more\n * importance when ranking search results. Use a field boost to specify that matches within\n * one field are more important than other fields.\n *\n * @param {string} fieldName - The name of a field to index in all documents.\n * @param {object} attributes - Optional attributes associated with this field.\n * @param {number} [attributes.boost=1] - Boost applied to all terms within this field.\n * @param {fieldExtractor} [attributes.extractor] - Function to extract a field from a document.\n * @throws {RangeError} fieldName cannot contain unsupported characters '/'\n */\nlunr.Builder.prototype.field = function (fieldName, attributes) {\n  if (/\\//.test(fieldName)) {\n    throw new RangeError (\"Field '\" + fieldName + \"' contains illegal character '/'\")\n  }\n\n  this._fields[fieldName] = attributes || {}\n}\n\n/**\n * A parameter to tune the amount of field length normalisation that is applied when\n * calculating relevance scores. A value of 0 will completely disable any normalisation\n * and a value of 1 will fully normalise field lengths. The default is 0.75. Values of b\n * will be clamped to the range 0 - 1.\n *\n * @param {number} number - The value to set for this tuning parameter.\n */\nlunr.Builder.prototype.b = function (number) {\n  if (number < 0) {\n    this._b = 0\n  } else if (number > 1) {\n    this._b = 1\n  } else {\n    this._b = number\n  }\n}\n\n/**\n * A parameter that controls the speed at which a rise in term frequency results in term\n * frequency saturation. The default value is 1.2. Setting this to a higher value will give\n * slower saturation levels, a lower value will result in quicker saturation.\n *\n * @param {number} number - The value to set for this tuning parameter.\n */\nlunr.Builder.prototype.k1 = function (number) {\n  this._k1 = number\n}\n\n/**\n * Adds a document to the index.\n *\n * Before adding fields to the index the index should have been fully setup, with the document\n * ref and all fields to index already having been specified.\n *\n * The document must have a field name as specified by the ref (by default this is 'id') and\n * it should have all fields defined for indexing, though null or undefined values will not\n * cause errors.\n *\n * Entire documents can be boosted at build time. Applying a boost to a document indicates that\n * this document should rank higher in search results than other documents.\n *\n * @param {object} doc - The document to add to the index.\n * @param {object} attributes - Optional attributes associated with this document.\n * @param {number} [attributes.boost=1] - Boost applied to all terms within this document.\n */\nlunr.Builder.prototype.add = function (doc, attributes) {\n  var docRef = doc[this._ref],\n      fields = Object.keys(this._fields)\n\n  this._documents[docRef] = attributes || {}\n  this.documentCount += 1\n\n  for (var i = 0; i < fields.length; i++) {\n    var fieldName = fields[i],\n        extractor = this._fields[fieldName].extractor,\n        field = extractor ? extractor(doc) : doc[fieldName],\n        tokens = this.tokenizer(field, {\n          fields: [fieldName]\n        }),\n        terms = this.pipeline.run(tokens),\n        fieldRef = new lunr.FieldRef (docRef, fieldName),\n        fieldTerms = Object.create(null)\n\n    this.fieldTermFrequencies[fieldRef] = fieldTerms\n    this.fieldLengths[fieldRef] = 0\n\n    // store the length of this field for this document\n    this.fieldLengths[fieldRef] += terms.length\n\n    // calculate term frequencies for this field\n    for (var j = 0; j < terms.length; j++) {\n      var term = terms[j]\n\n      if (fieldTerms[term] == undefined) {\n        fieldTerms[term] = 0\n      }\n\n      fieldTerms[term] += 1\n\n      // add to inverted index\n      // create an initial posting if one doesn't exist\n      if (this.invertedIndex[term] == undefined) {\n        var posting = Object.create(null)\n        posting[\"_index\"] = this.termIndex\n        this.termIndex += 1\n\n        for (var k = 0; k < fields.length; k++) {\n          posting[fields[k]] = Object.create(null)\n        }\n\n        this.invertedIndex[term] = posting\n      }\n\n      // add an entry for this term/fieldName/docRef to the invertedIndex\n      if (this.invertedIndex[term][fieldName][docRef] == undefined) {\n        this.invertedIndex[term][fieldName][docRef] = Object.create(null)\n      }\n\n      // store all whitelisted metadata about this token in the\n      // inverted index\n      for (var l = 0; l < this.metadataWhitelist.length; l++) {\n        var metadataKey = this.metadataWhitelist[l],\n            metadata = term.metadata[metadataKey]\n\n        if (this.invertedIndex[term][fieldName][docRef][metadataKey] == undefined) {\n          this.invertedIndex[term][fieldName][docRef][metadataKey] = []\n        }\n\n        this.invertedIndex[term][fieldName][docRef][metadataKey].push(metadata)\n      }\n    }\n\n  }\n}\n\n/**\n * Calculates the average document length for this index\n *\n * @private\n */\nlunr.Builder.prototype.calculateAverageFieldLengths = function () {\n\n  var fieldRefs = Object.keys(this.fieldLengths),\n      numberOfFields = fieldRefs.length,\n      accumulator = {},\n      documentsWithField = {}\n\n  for (var i = 0; i < numberOfFields; i++) {\n    var fieldRef = lunr.FieldRef.fromString(fieldRefs[i]),\n        field = fieldRef.fieldName\n\n    documentsWithField[field] || (documentsWithField[field] = 0)\n    documentsWithField[field] += 1\n\n    accumulator[field] || (accumulator[field] = 0)\n    accumulator[field] += this.fieldLengths[fieldRef]\n  }\n\n  var fields = Object.keys(this._fields)\n\n  for (var i = 0; i < fields.length; i++) {\n    var fieldName = fields[i]\n    accumulator[fieldName] = accumulator[fieldName] / documentsWithField[fieldName]\n  }\n\n  this.averageFieldLength = accumulator\n}\n\n/**\n * Builds a vector space model of every document using lunr.Vector\n *\n * @private\n */\nlunr.Builder.prototype.createFieldVectors = function () {\n  var fieldVectors = {},\n      fieldRefs = Object.keys(this.fieldTermFrequencies),\n      fieldRefsLength = fieldRefs.length,\n      termIdfCache = Object.create(null)\n\n  for (var i = 0; i < fieldRefsLength; i++) {\n    var fieldRef = lunr.FieldRef.fromString(fieldRefs[i]),\n        fieldName = fieldRef.fieldName,\n        fieldLength = this.fieldLengths[fieldRef],\n        fieldVector = new lunr.Vector,\n        termFrequencies = this.fieldTermFrequencies[fieldRef],\n        terms = Object.keys(termFrequencies),\n        termsLength = terms.length\n\n\n    var fieldBoost = this._fields[fieldName].boost || 1,\n        docBoost = this._documents[fieldRef.docRef].boost || 1\n\n    for (var j = 0; j < termsLength; j++) {\n      var term = terms[j],\n          tf = termFrequencies[term],\n          termIndex = this.invertedIndex[term]._index,\n          idf, score, scoreWithPrecision\n\n      if (termIdfCache[term] === undefined) {\n        idf = lunr.idf(this.invertedIndex[term], this.documentCount)\n        termIdfCache[term] = idf\n      } else {\n        idf = termIdfCache[term]\n      }\n\n      score = idf * ((this._k1 + 1) * tf) / (this._k1 * (1 - this._b + this._b * (fieldLength / this.averageFieldLength[fieldName])) + tf)\n      score *= fieldBoost\n      score *= docBoost\n      scoreWithPrecision = Math.round(score * 1000) / 1000\n      // Converts 1.23456789 to 1.234.\n      // Reducing the precision so that the vectors take up less\n      // space when serialised. Doing it now so that they behave\n      // the same before and after serialisation. Also, this is\n      // the fastest approach to reducing a number's precision in\n      // JavaScript.\n\n      fieldVector.insert(termIndex, scoreWithPrecision)\n    }\n\n    fieldVectors[fieldRef] = fieldVector\n  }\n\n  this.fieldVectors = fieldVectors\n}\n\n/**\n * Creates a token set of all tokens in the index using lunr.TokenSet\n *\n * @private\n */\nlunr.Builder.prototype.createTokenSet = function () {\n  this.tokenSet = lunr.TokenSet.fromArray(\n    Object.keys(this.invertedIndex).sort()\n  )\n}\n\n/**\n * Builds the index, creating an instance of lunr.Index.\n *\n * This completes the indexing process and should only be called\n * once all documents have been added to the index.\n *\n * @returns {lunr.Index}\n */\nlunr.Builder.prototype.build = function () {\n  this.calculateAverageFieldLengths()\n  this.createFieldVectors()\n  this.createTokenSet()\n\n  return new lunr.Index({\n    invertedIndex: this.invertedIndex,\n    fieldVectors: this.fieldVectors,\n    tokenSet: this.tokenSet,\n    fields: Object.keys(this._fields),\n    pipeline: this.searchPipeline\n  })\n}\n\n/**\n * Applies a plugin to the index builder.\n *\n * A plugin is a function that is called with the index builder as its context.\n * Plugins can be used to customise or extend the behaviour of the index\n * in some way. A plugin is just a function, that encapsulated the custom\n * behaviour that should be applied when building the index.\n *\n * The plugin function will be called with the index builder as its argument, additional\n * arguments can also be passed when calling use. The function will be called\n * with the index builder as its context.\n *\n * @param {Function} plugin The plugin to apply.\n */\nlunr.Builder.prototype.use = function (fn) {\n  var args = Array.prototype.slice.call(arguments, 1)\n  args.unshift(this)\n  fn.apply(this, args)\n}\n/**\n * Contains and collects metadata about a matching document.\n * A single instance of lunr.MatchData is returned as part of every\n * lunr.Index~Result.\n *\n * @constructor\n * @param {string} term - The term this match data is associated with\n * @param {string} field - The field in which the term was found\n * @param {object} metadata - The metadata recorded about this term in this field\n * @property {object} metadata - A cloned collection of metadata associated with this document.\n * @see {@link lunr.Index~Result}\n */\nlunr.MatchData = function (term, field, metadata) {\n  var clonedMetadata = Object.create(null),\n      metadataKeys = Object.keys(metadata || {})\n\n  // Cloning the metadata to prevent the original\n  // being mutated during match data combination.\n  // Metadata is kept in an array within the inverted\n  // index so cloning the data can be done with\n  // Array#slice\n  for (var i = 0; i < metadataKeys.length; i++) {\n    var key = metadataKeys[i]\n    clonedMetadata[key] = metadata[key].slice()\n  }\n\n  this.metadata = Object.create(null)\n\n  if (term !== undefined) {\n    this.metadata[term] = Object.create(null)\n    this.metadata[term][field] = clonedMetadata\n  }\n}\n\n/**\n * An instance of lunr.MatchData will be created for every term that matches a\n * document. However only one instance is required in a lunr.Index~Result. This\n * method combines metadata from another instance of lunr.MatchData with this\n * objects metadata.\n *\n * @param {lunr.MatchData} otherMatchData - Another instance of match data to merge with this one.\n * @see {@link lunr.Index~Result}\n */\nlunr.MatchData.prototype.combine = function (otherMatchData) {\n  var terms = Object.keys(otherMatchData.metadata)\n\n  for (var i = 0; i < terms.length; i++) {\n    var term = terms[i],\n        fields = Object.keys(otherMatchData.metadata[term])\n\n    if (this.metadata[term] == undefined) {\n      this.metadata[term] = Object.create(null)\n    }\n\n    for (var j = 0; j < fields.length; j++) {\n      var field = fields[j],\n          keys = Object.keys(otherMatchData.metadata[term][field])\n\n      if (this.metadata[term][field] == undefined) {\n        this.metadata[term][field] = Object.create(null)\n      }\n\n      for (var k = 0; k < keys.length; k++) {\n        var key = keys[k]\n\n        if (this.metadata[term][field][key] == undefined) {\n          this.metadata[term][field][key] = otherMatchData.metadata[term][field][key]\n        } else {\n          this.metadata[term][field][key] = this.metadata[term][field][key].concat(otherMatchData.metadata[term][field][key])\n        }\n\n      }\n    }\n  }\n}\n\n/**\n * Add metadata for a term/field pair to this instance of match data.\n *\n * @param {string} term - The term this match data is associated with\n * @param {string} field - The field in which the term was found\n * @param {object} metadata - The metadata recorded about this term in this field\n */\nlunr.MatchData.prototype.add = function (term, field, metadata) {\n  if (!(term in this.metadata)) {\n    this.metadata[term] = Object.create(null)\n    this.metadata[term][field] = metadata\n    return\n  }\n\n  if (!(field in this.metadata[term])) {\n    this.metadata[term][field] = metadata\n    return\n  }\n\n  var metadataKeys = Object.keys(metadata)\n\n  for (var i = 0; i < metadataKeys.length; i++) {\n    var key = metadataKeys[i]\n\n    if (key in this.metadata[term][field]) {\n      this.metadata[term][field][key] = this.metadata[term][field][key].concat(metadata[key])\n    } else {\n      this.metadata[term][field][key] = metadata[key]\n    }\n  }\n}\n/**\n * A lunr.Query provides a programmatic way of defining queries to be performed\n * against a {@link lunr.Index}.\n *\n * Prefer constructing a lunr.Query using the {@link lunr.Index#query} method\n * so the query object is pre-initialized with the right index fields.\n *\n * @constructor\n * @property {lunr.Query~Clause[]} clauses - An array of query clauses.\n * @property {string[]} allFields - An array of all available fields in a lunr.Index.\n */\nlunr.Query = function (allFields) {\n  this.clauses = []\n  this.allFields = allFields\n}\n\n/**\n * Constants for indicating what kind of automatic wildcard insertion will be used when constructing a query clause.\n *\n * This allows wildcards to be added to the beginning and end of a term without having to manually do any string\n * concatenation.\n *\n * The wildcard constants can be bitwise combined to select both leading and trailing wildcards.\n *\n * @constant\n * @default\n * @property {number} wildcard.NONE - The term will have no wildcards inserted, this is the default behaviour\n * @property {number} wildcard.LEADING - Prepend the term with a wildcard, unless a leading wildcard already exists\n * @property {number} wildcard.TRAILING - Append a wildcard to the term, unless a trailing wildcard already exists\n * @see lunr.Query~Clause\n * @see lunr.Query#clause\n * @see lunr.Query#term\n * @example <caption>query term with trailing wildcard</caption>\n * query.term('foo', { wildcard: lunr.Query.wildcard.TRAILING })\n * @example <caption>query term with leading and trailing wildcard</caption>\n * query.term('foo', {\n *   wildcard: lunr.Query.wildcard.LEADING | lunr.Query.wildcard.TRAILING\n * })\n */\n\nlunr.Query.wildcard = new String (\"*\")\nlunr.Query.wildcard.NONE = 0\nlunr.Query.wildcard.LEADING = 1\nlunr.Query.wildcard.TRAILING = 2\n\n/**\n * Constants for indicating what kind of presence a term must have in matching documents.\n *\n * @constant\n * @enum {number}\n * @see lunr.Query~Clause\n * @see lunr.Query#clause\n * @see lunr.Query#term\n * @example <caption>query term with required presence</caption>\n * query.term('foo', { presence: lunr.Query.presence.REQUIRED })\n */\nlunr.Query.presence = {\n  /**\n   * Term's presence in a document is optional, this is the default value.\n   */\n  OPTIONAL: 1,\n\n  /**\n   * Term's presence in a document is required, documents that do not contain\n   * this term will not be returned.\n   */\n  REQUIRED: 2,\n\n  /**\n   * Term's presence in a document is prohibited, documents that do contain\n   * this term will not be returned.\n   */\n  PROHIBITED: 3\n}\n\n/**\n * A single clause in a {@link lunr.Query} contains a term and details on how to\n * match that term against a {@link lunr.Index}.\n *\n * @typedef {Object} lunr.Query~Clause\n * @property {string[]} fields - The fields in an index this clause should be matched against.\n * @property {number} [boost=1] - Any boost that should be applied when matching this clause.\n * @property {number} [editDistance] - Whether the term should have fuzzy matching applied, and how fuzzy the match should be.\n * @property {boolean} [usePipeline] - Whether the term should be passed through the search pipeline.\n * @property {number} [wildcard=lunr.Query.wildcard.NONE] - Whether the term should have wildcards appended or prepended.\n * @property {number} [presence=lunr.Query.presence.OPTIONAL] - The terms presence in any matching documents.\n */\n\n/**\n * Adds a {@link lunr.Query~Clause} to this query.\n *\n * Unless the clause contains the fields to be matched all fields will be matched. In addition\n * a default boost of 1 is applied to the clause.\n *\n * @param {lunr.Query~Clause} clause - The clause to add to this query.\n * @see lunr.Query~Clause\n * @returns {lunr.Query}\n */\nlunr.Query.prototype.clause = function (clause) {\n  if (!('fields' in clause)) {\n    clause.fields = this.allFields\n  }\n\n  if (!('boost' in clause)) {\n    clause.boost = 1\n  }\n\n  if (!('usePipeline' in clause)) {\n    clause.usePipeline = true\n  }\n\n  if (!('wildcard' in clause)) {\n    clause.wildcard = lunr.Query.wildcard.NONE\n  }\n\n  if ((clause.wildcard & lunr.Query.wildcard.LEADING) && (clause.term.charAt(0) != lunr.Query.wildcard)) {\n    clause.term = \"*\" + clause.term\n  }\n\n  if ((clause.wildcard & lunr.Query.wildcard.TRAILING) && (clause.term.slice(-1) != lunr.Query.wildcard)) {\n    clause.term = \"\" + clause.term + \"*\"\n  }\n\n  if (!('presence' in clause)) {\n    clause.presence = lunr.Query.presence.OPTIONAL\n  }\n\n  this.clauses.push(clause)\n\n  return this\n}\n\n/**\n * A negated query is one in which every clause has a presence of\n * prohibited. These queries require some special processing to return\n * the expected results.\n *\n * @returns boolean\n */\nlunr.Query.prototype.isNegated = function () {\n  for (var i = 0; i < this.clauses.length; i++) {\n    if (this.clauses[i].presence != lunr.Query.presence.PROHIBITED) {\n      return false\n    }\n  }\n\n  return true\n}\n\n/**\n * Adds a term to the current query, under the covers this will create a {@link lunr.Query~Clause}\n * to the list of clauses that make up this query.\n *\n * The term is used as is, i.e. no tokenization will be performed by this method. Instead conversion\n * to a token or token-like string should be done before calling this method.\n *\n * The term will be converted to a string by calling `toString`. Multiple terms can be passed as an\n * array, each term in the array will share the same options.\n *\n * @param {object|object[]} term - The term(s) to add to the query.\n * @param {object} [options] - Any additional properties to add to the query clause.\n * @returns {lunr.Query}\n * @see lunr.Query#clause\n * @see lunr.Query~Clause\n * @example <caption>adding a single term to a query</caption>\n * query.term(\"foo\")\n * @example <caption>adding a single term to a query and specifying search fields, term boost and automatic trailing wildcard</caption>\n * query.term(\"foo\", {\n *   fields: [\"title\"],\n *   boost: 10,\n *   wildcard: lunr.Query.wildcard.TRAILING\n * })\n * @example <caption>using lunr.tokenizer to convert a string to tokens before using them as terms</caption>\n * query.term(lunr.tokenizer(\"foo bar\"))\n */\nlunr.Query.prototype.term = function (term, options) {\n  if (Array.isArray(term)) {\n    term.forEach(function (t) { this.term(t, lunr.utils.clone(options)) }, this)\n    return this\n  }\n\n  var clause = options || {}\n  clause.term = term.toString()\n\n  this.clause(clause)\n\n  return this\n}\nlunr.QueryParseError = function (message, start, end) {\n  this.name = \"QueryParseError\"\n  this.message = message\n  this.start = start\n  this.end = end\n}\n\nlunr.QueryParseError.prototype = new Error\nlunr.QueryLexer = function (str) {\n  this.lexemes = []\n  this.str = str\n  this.length = str.length\n  this.pos = 0\n  this.start = 0\n  this.escapeCharPositions = []\n}\n\nlunr.QueryLexer.prototype.run = function () {\n  var state = lunr.QueryLexer.lexText\n\n  while (state) {\n    state = state(this)\n  }\n}\n\nlunr.QueryLexer.prototype.sliceString = function () {\n  var subSlices = [],\n      sliceStart = this.start,\n      sliceEnd = this.pos\n\n  for (var i = 0; i < this.escapeCharPositions.length; i++) {\n    sliceEnd = this.escapeCharPositions[i]\n    subSlices.push(this.str.slice(sliceStart, sliceEnd))\n    sliceStart = sliceEnd + 1\n  }\n\n  subSlices.push(this.str.slice(sliceStart, this.pos))\n  this.escapeCharPositions.length = 0\n\n  return subSlices.join('')\n}\n\nlunr.QueryLexer.prototype.emit = function (type) {\n  this.lexemes.push({\n    type: type,\n    str: this.sliceString(),\n    start: this.start,\n    end: this.pos\n  })\n\n  this.start = this.pos\n}\n\nlunr.QueryLexer.prototype.escapeCharacter = function () {\n  this.escapeCharPositions.push(this.pos - 1)\n  this.pos += 1\n}\n\nlunr.QueryLexer.prototype.next = function () {\n  if (this.pos >= this.length) {\n    return lunr.QueryLexer.EOS\n  }\n\n  var char = this.str.charAt(this.pos)\n  this.pos += 1\n  return char\n}\n\nlunr.QueryLexer.prototype.width = function () {\n  return this.pos - this.start\n}\n\nlunr.QueryLexer.prototype.ignore = function () {\n  if (this.start == this.pos) {\n    this.pos += 1\n  }\n\n  this.start = this.pos\n}\n\nlunr.QueryLexer.prototype.backup = function () {\n  this.pos -= 1\n}\n\nlunr.QueryLexer.prototype.acceptDigitRun = function () {\n  var char, charCode\n\n  do {\n    char = this.next()\n    charCode = char.charCodeAt(0)\n  } while (charCode > 47 && charCode < 58)\n\n  if (char != lunr.QueryLexer.EOS) {\n    this.backup()\n  }\n}\n\nlunr.QueryLexer.prototype.more = function () {\n  return this.pos < this.length\n}\n\nlunr.QueryLexer.EOS = 'EOS'\nlunr.QueryLexer.FIELD = 'FIELD'\nlunr.QueryLexer.TERM = 'TERM'\nlunr.QueryLexer.EDIT_DISTANCE = 'EDIT_DISTANCE'\nlunr.QueryLexer.BOOST = 'BOOST'\nlunr.QueryLexer.PRESENCE = 'PRESENCE'\n\nlunr.QueryLexer.lexField = function (lexer) {\n  lexer.backup()\n  lexer.emit(lunr.QueryLexer.FIELD)\n  lexer.ignore()\n  return lunr.QueryLexer.lexText\n}\n\nlunr.QueryLexer.lexTerm = function (lexer) {\n  if (lexer.width() > 1) {\n    lexer.backup()\n    lexer.emit(lunr.QueryLexer.TERM)\n  }\n\n  lexer.ignore()\n\n  if (lexer.more()) {\n    return lunr.QueryLexer.lexText\n  }\n}\n\nlunr.QueryLexer.lexEditDistance = function (lexer) {\n  lexer.ignore()\n  lexer.acceptDigitRun()\n  lexer.emit(lunr.QueryLexer.EDIT_DISTANCE)\n  return lunr.QueryLexer.lexText\n}\n\nlunr.QueryLexer.lexBoost = function (lexer) {\n  lexer.ignore()\n  lexer.acceptDigitRun()\n  lexer.emit(lunr.QueryLexer.BOOST)\n  return lunr.QueryLexer.lexText\n}\n\nlunr.QueryLexer.lexEOS = function (lexer) {\n  if (lexer.width() > 0) {\n    lexer.emit(lunr.QueryLexer.TERM)\n  }\n}\n\n// This matches the separator used when tokenising fields\n// within a document. These should match otherwise it is\n// not possible to search for some tokens within a document.\n//\n// It is possible for the user to change the separator on the\n// tokenizer so it _might_ clash with any other of the special\n// characters already used within the search string, e.g. :.\n//\n// This means that it is possible to change the separator in\n// such a way that makes some words unsearchable using a search\n// string.\nlunr.QueryLexer.termSeparator = lunr.tokenizer.separator\n\nlunr.QueryLexer.lexText = function (lexer) {\n  while (true) {\n    var char = lexer.next()\n\n    if (char == lunr.QueryLexer.EOS) {\n      return lunr.QueryLexer.lexEOS\n    }\n\n    // Escape character is '\\'\n    if (char.charCodeAt(0) == 92) {\n      lexer.escapeCharacter()\n      continue\n    }\n\n    if (char == \":\") {\n      return lunr.QueryLexer.lexField\n    }\n\n    if (char == \"~\") {\n      lexer.backup()\n      if (lexer.width() > 0) {\n        lexer.emit(lunr.QueryLexer.TERM)\n      }\n      return lunr.QueryLexer.lexEditDistance\n    }\n\n    if (char == \"^\") {\n      lexer.backup()\n      if (lexer.width() > 0) {\n        lexer.emit(lunr.QueryLexer.TERM)\n      }\n      return lunr.QueryLexer.lexBoost\n    }\n\n    // \"+\" indicates term presence is required\n    // checking for length to ensure that only\n    // leading \"+\" are considered\n    if (char == \"+\" && lexer.width() === 1) {\n      lexer.emit(lunr.QueryLexer.PRESENCE)\n      return lunr.QueryLexer.lexText\n    }\n\n    // \"-\" indicates term presence is prohibited\n    // checking for length to ensure that only\n    // leading \"-\" are considered\n    if (char == \"-\" && lexer.width() === 1) {\n      lexer.emit(lunr.QueryLexer.PRESENCE)\n      return lunr.QueryLexer.lexText\n    }\n\n    if (char.match(lunr.QueryLexer.termSeparator)) {\n      return lunr.QueryLexer.lexTerm\n    }\n  }\n}\n\nlunr.QueryParser = function (str, query) {\n  this.lexer = new lunr.QueryLexer (str)\n  this.query = query\n  this.currentClause = {}\n  this.lexemeIdx = 0\n}\n\nlunr.QueryParser.prototype.parse = function () {\n  this.lexer.run()\n  this.lexemes = this.lexer.lexemes\n\n  var state = lunr.QueryParser.parseClause\n\n  while (state) {\n    state = state(this)\n  }\n\n  return this.query\n}\n\nlunr.QueryParser.prototype.peekLexeme = function () {\n  return this.lexemes[this.lexemeIdx]\n}\n\nlunr.QueryParser.prototype.consumeLexeme = function () {\n  var lexeme = this.peekLexeme()\n  this.lexemeIdx += 1\n  return lexeme\n}\n\nlunr.QueryParser.prototype.nextClause = function () {\n  var completedClause = this.currentClause\n  this.query.clause(completedClause)\n  this.currentClause = {}\n}\n\nlunr.QueryParser.parseClause = function (parser) {\n  var lexeme = parser.peekLexeme()\n\n  if (lexeme == undefined) {\n    return\n  }\n\n  switch (lexeme.type) {\n    case lunr.QueryLexer.PRESENCE:\n      return lunr.QueryParser.parsePresence\n    case lunr.QueryLexer.FIELD:\n      return lunr.QueryParser.parseField\n    case lunr.QueryLexer.TERM:\n      return lunr.QueryParser.parseTerm\n    default:\n      var errorMessage = \"expected either a field or a term, found \" + lexeme.type\n\n      if (lexeme.str.length >= 1) {\n        errorMessage += \" with value '\" + lexeme.str + \"'\"\n      }\n\n      throw new lunr.QueryParseError (errorMessage, lexeme.start, lexeme.end)\n  }\n}\n\nlunr.QueryParser.parsePresence = function (parser) {\n  var lexeme = parser.consumeLexeme()\n\n  if (lexeme == undefined) {\n    return\n  }\n\n  switch (lexeme.str) {\n    case \"-\":\n      parser.currentClause.presence = lunr.Query.presence.PROHIBITED\n      break\n    case \"+\":\n      parser.currentClause.presence = lunr.Query.presence.REQUIRED\n      break\n    default:\n      var errorMessage = \"unrecognised presence operator'\" + lexeme.str + \"'\"\n      throw new lunr.QueryParseError (errorMessage, lexeme.start, lexeme.end)\n  }\n\n  var nextLexeme = parser.peekLexeme()\n\n  if (nextLexeme == undefined) {\n    var errorMessage = \"expecting term or field, found nothing\"\n    throw new lunr.QueryParseError (errorMessage, lexeme.start, lexeme.end)\n  }\n\n  switch (nextLexeme.type) {\n    case lunr.QueryLexer.FIELD:\n      return lunr.QueryParser.parseField\n    case lunr.QueryLexer.TERM:\n      return lunr.QueryParser.parseTerm\n    default:\n      var errorMessage = \"expecting term or field, found '\" + nextLexeme.type + \"'\"\n      throw new lunr.QueryParseError (errorMessage, nextLexeme.start, nextLexeme.end)\n  }\n}\n\nlunr.QueryParser.parseField = function (parser) {\n  var lexeme = parser.consumeLexeme()\n\n  if (lexeme == undefined) {\n    return\n  }\n\n  if (parser.query.allFields.indexOf(lexeme.str) == -1) {\n    var possibleFields = parser.query.allFields.map(function (f) { return \"'\" + f + \"'\" }).join(', '),\n        errorMessage = \"unrecognised field '\" + lexeme.str + \"', possible fields: \" + possibleFields\n\n    throw new lunr.QueryParseError (errorMessage, lexeme.start, lexeme.end)\n  }\n\n  parser.currentClause.fields = [lexeme.str]\n\n  var nextLexeme = parser.peekLexeme()\n\n  if (nextLexeme == undefined) {\n    var errorMessage = \"expecting term, found nothing\"\n    throw new lunr.QueryParseError (errorMessage, lexeme.start, lexeme.end)\n  }\n\n  switch (nextLexeme.type) {\n    case lunr.QueryLexer.TERM:\n      return lunr.QueryParser.parseTerm\n    default:\n      var errorMessage = \"expecting term, found '\" + nextLexeme.type + \"'\"\n      throw new lunr.QueryParseError (errorMessage, nextLexeme.start, nextLexeme.end)\n  }\n}\n\nlunr.QueryParser.parseTerm = function (parser) {\n  var lexeme = parser.consumeLexeme()\n\n  if (lexeme == undefined) {\n    return\n  }\n\n  parser.currentClause.term = lexeme.str.toLowerCase()\n\n  if (lexeme.str.indexOf(\"*\") != -1) {\n    parser.currentClause.usePipeline = false\n  }\n\n  var nextLexeme = parser.peekLexeme()\n\n  if (nextLexeme == undefined) {\n    parser.nextClause()\n    return\n  }\n\n  switch (nextLexeme.type) {\n    case lunr.QueryLexer.TERM:\n      parser.nextClause()\n      return lunr.QueryParser.parseTerm\n    case lunr.QueryLexer.FIELD:\n      parser.nextClause()\n      return lunr.QueryParser.parseField\n    case lunr.QueryLexer.EDIT_DISTANCE:\n      return lunr.QueryParser.parseEditDistance\n    case lunr.QueryLexer.BOOST:\n      return lunr.QueryParser.parseBoost\n    case lunr.QueryLexer.PRESENCE:\n      parser.nextClause()\n      return lunr.QueryParser.parsePresence\n    default:\n      var errorMessage = \"Unexpected lexeme type '\" + nextLexeme.type + \"'\"\n      throw new lunr.QueryParseError (errorMessage, nextLexeme.start, nextLexeme.end)\n  }\n}\n\nlunr.QueryParser.parseEditDistance = function (parser) {\n  var lexeme = parser.consumeLexeme()\n\n  if (lexeme == undefined) {\n    return\n  }\n\n  var editDistance = parseInt(lexeme.str, 10)\n\n  if (isNaN(editDistance)) {\n    var errorMessage = \"edit distance must be numeric\"\n    throw new lunr.QueryParseError (errorMessage, lexeme.start, lexeme.end)\n  }\n\n  parser.currentClause.editDistance = editDistance\n\n  var nextLexeme = parser.peekLexeme()\n\n  if (nextLexeme == undefined) {\n    parser.nextClause()\n    return\n  }\n\n  switch (nextLexeme.type) {\n    case lunr.QueryLexer.TERM:\n      parser.nextClause()\n      return lunr.QueryParser.parseTerm\n    case lunr.QueryLexer.FIELD:\n      parser.nextClause()\n      return lunr.QueryParser.parseField\n    case lunr.QueryLexer.EDIT_DISTANCE:\n      return lunr.QueryParser.parseEditDistance\n    case lunr.QueryLexer.BOOST:\n      return lunr.QueryParser.parseBoost\n    case lunr.QueryLexer.PRESENCE:\n      parser.nextClause()\n      return lunr.QueryParser.parsePresence\n    default:\n      var errorMessage = \"Unexpected lexeme type '\" + nextLexeme.type + \"'\"\n      throw new lunr.QueryParseError (errorMessage, nextLexeme.start, nextLexeme.end)\n  }\n}\n\nlunr.QueryParser.parseBoost = function (parser) {\n  var lexeme = parser.consumeLexeme()\n\n  if (lexeme == undefined) {\n    return\n  }\n\n  var boost = parseInt(lexeme.str, 10)\n\n  if (isNaN(boost)) {\n    var errorMessage = \"boost must be numeric\"\n    throw new lunr.QueryParseError (errorMessage, lexeme.start, lexeme.end)\n  }\n\n  parser.currentClause.boost = boost\n\n  var nextLexeme = parser.peekLexeme()\n\n  if (nextLexeme == undefined) {\n    parser.nextClause()\n    return\n  }\n\n  switch (nextLexeme.type) {\n    case lunr.QueryLexer.TERM:\n      parser.nextClause()\n      return lunr.QueryParser.parseTerm\n    case lunr.QueryLexer.FIELD:\n      parser.nextClause()\n      return lunr.QueryParser.parseField\n    case lunr.QueryLexer.EDIT_DISTANCE:\n      return lunr.QueryParser.parseEditDistance\n    case lunr.QueryLexer.BOOST:\n      return lunr.QueryParser.parseBoost\n    case lunr.QueryLexer.PRESENCE:\n      parser.nextClause()\n      return lunr.QueryParser.parsePresence\n    default:\n      var errorMessage = \"Unexpected lexeme type '\" + nextLexeme.type + \"'\"\n      throw new lunr.QueryParseError (errorMessage, nextLexeme.start, nextLexeme.end)\n  }\n}\n\n  /**\n   * export the module via AMD, CommonJS or as a browser global\n   * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js\n   */\n  ;(function (root, factory) {\n    if (true) {\n      // AMD. Register as an anonymous module.\n      !(__WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.call(exports, __webpack_require__, exports, module)) :\n\t\t__WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__))\n    } else {}\n  }(this, function () {\n    /**\n     * Just return a value to define the module export.\n     * This example returns an object, but the module\n     * can return a function as the exported value.\n     */\n    return lunr\n  }))\n})();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lunr/lunr.js\n");

/***/ })

};
;