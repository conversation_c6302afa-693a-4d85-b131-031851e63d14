import { SearchService } from "../services/search-service.ts";
import path from "path";

async function main() {
  const service = new SearchService();
  const index = await service.buildSearchIndex();

  const outputPath = path.join(process.cwd(), "public/search-index.json");
  await service.saveIndexToFile(index, outputPath);

  console.log(
    `Search index built with ${Object.keys(index.documents).length} documents`
  );
}

main().catch(console.error);
