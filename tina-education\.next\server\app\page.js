/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/components/NotificationToast.tsx":
/*!**********************************************!*\
  !*** ./app/components/NotificationToast.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\NotificationToast.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/components/Search.tsx":
/*!***********************************!*\
  !*** ./app/components/Search.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SearchBox: () => (/* binding */ SearchBox)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const SearchBox = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SearchBox() from the server but SearchBox is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\Search.tsx",
"SearchBox",
);

/***/ }),

/***/ "(rsc)/./app/components/footer.tsx":
/*!***********************************!*\
  !*** ./app/components/footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto w-[90%] max-w-7xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-bold mb-4\",\n                                        children: \"Quick Links\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                        lineNumber: 10,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/\",\n                                                    className: \"text-gray-300 hover:text-white\",\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 13,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 12,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"#\",\n                                                    className: \"text-gray-300 hover:text-white\",\n                                                    children: \"About\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 18,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 17,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/books\",\n                                                    className: \"text-gray-300 hover:text-white\",\n                                                    children: \"Books\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 23,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 22,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/journals\",\n                                                    className: \"text-gray-300 hover:text-white\",\n                                                    children: \"Journals\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 31,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 30,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/articles\",\n                                                    className: \"text-gray-300 hover:text-white\",\n                                                    children: \"Articles\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 39,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 38,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"#\",\n                                                    className: \"text-gray-300 hover:text-white\",\n                                                    children: \"Privacy Policy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 47,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                        lineNumber: 11,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                lineNumber: 9,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                            lineNumber: 8,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold mb-4\",\n                                    children: \"Services\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"#\",\n                                                className: \"text-gray-300 hover:text-white\",\n                                                children: \"Publishing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"#\",\n                                                className: \"text-gray-300 hover:text-white\",\n                                                children: \"Editing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"#\",\n                                                className: \"text-gray-300 hover:text-white\",\n                                                children: \"Formatting\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold mb-4\",\n                                    children: \"Newsletter Sign Up\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            placeholder: \"Enter your Email\",\n                                            className: \"bg-gray-600 px-4 py-2 w-full rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"bg-white text-gray-900 ms-2 px-4 py-2 rounded\",\n                                            children: \"Submit\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 pt-8 border-t border-gray-700 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Copyright \\xa9 2025 - Tina Education\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvY29tcG9uZW50cy9mb290ZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE2QjtBQUVkLFNBQVNDO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFPQyxXQUFVO2tCQUNoQiw0RUFBQ0M7WUFBSUQsV0FBVTs7OEJBQ2IsOERBQUNDO29CQUFJRCxXQUFVOztzQ0FDYiw4REFBQ0M7c0NBQ0MsNEVBQUNBOztrREFDQyw4REFBQ0M7d0NBQUdGLFdBQVU7a0RBQXlCOzs7Ozs7a0RBQ3ZDLDhEQUFDRzt3Q0FBR0gsV0FBVTs7MERBQ1osOERBQUNJOzBEQUNDLDRFQUFDUCxrREFBSUE7b0RBQUNRLE1BQUs7b0RBQUlMLFdBQVU7OERBQWlDOzs7Ozs7Ozs7OzswREFJNUQsOERBQUNJOzBEQUNDLDRFQUFDUCxrREFBSUE7b0RBQUNRLE1BQUs7b0RBQUlMLFdBQVU7OERBQWlDOzs7Ozs7Ozs7OzswREFJNUQsOERBQUNJOzBEQUNDLDRFQUFDUCxrREFBSUE7b0RBQ0hRLE1BQUs7b0RBQ0xMLFdBQVU7OERBQ1g7Ozs7Ozs7Ozs7OzBEQUlILDhEQUFDSTswREFDQyw0RUFBQ1Asa0RBQUlBO29EQUNIUSxNQUFLO29EQUNMTCxXQUFVOzhEQUNYOzs7Ozs7Ozs7OzswREFJSCw4REFBQ0k7MERBQ0MsNEVBQUNQLGtEQUFJQTtvREFDSFEsTUFBSztvREFDTEwsV0FBVTs4REFDWDs7Ozs7Ozs7Ozs7MERBSUgsOERBQUNJOzBEQUNDLDRFQUFDUCxrREFBSUE7b0RBQUNRLE1BQUs7b0RBQUlMLFdBQVU7OERBQWlDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQVFsRSw4REFBQ0M7OzhDQUNDLDhEQUFDQztvQ0FBR0YsV0FBVTs4Q0FBeUI7Ozs7Ozs4Q0FDdkMsOERBQUNHO29DQUFHSCxXQUFVOztzREFDWiw4REFBQ0k7c0RBQ0MsNEVBQUNQLGtEQUFJQTtnREFBQ1EsTUFBSztnREFBSUwsV0FBVTswREFBaUM7Ozs7Ozs7Ozs7O3NEQUk1RCw4REFBQ0k7c0RBQ0MsNEVBQUNQLGtEQUFJQTtnREFBQ1EsTUFBSztnREFBSUwsV0FBVTswREFBaUM7Ozs7Ozs7Ozs7O3NEQUk1RCw4REFBQ0k7c0RBQ0MsNEVBQUNQLGtEQUFJQTtnREFBQ1EsTUFBSztnREFBSUwsV0FBVTswREFBaUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU1oRSw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDRTtvQ0FBR0YsV0FBVTs4Q0FBeUI7Ozs7Ozs4Q0FDdkMsOERBQUNDO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ007NENBQ0NDLE1BQUs7NENBQ0xDLGFBQVk7NENBQ1pSLFdBQVU7Ozs7OztzREFFWiw4REFBQ1M7NENBQU9ULFdBQVU7c0RBQWdEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBTXhFLDhEQUFDQztvQkFBSUQsV0FBVTs4QkFDYiw0RUFBQ1U7d0JBQUVWLFdBQVU7a0NBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS3ZDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJpY2hlXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXEFSUlMtTmV4dEpTXFx0aW5hLWVkdWNhdGlvblxcYXBwXFxjb21wb25lbnRzXFxmb290ZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBMaW5rIGZyb20gXCJuZXh0L2xpbmtcIjtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEZvb3RlcigpIHtcclxuICByZXR1cm4gKFxyXG4gICAgPGZvb3RlciBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMCB0ZXh0LXdoaXRlIHB5LThcIj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byB3LVs5MCVdIG1heC13LTd4bFwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMyBnYXAtNFwiPlxyXG4gICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgbWItNFwiPlF1aWNrIExpbmtzPC9oMz5cclxuICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0xXCI+XHJcbiAgICAgICAgICAgICAgICA8bGk+XHJcbiAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXdoaXRlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgSG9tZVxyXG4gICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgICAgPGxpPlxyXG4gICAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiI1wiIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC13aGl0ZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgIEFib3V0XHJcbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgICA8bGk+XHJcbiAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9ib29rc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXdoaXRlXCJcclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIEJvb2tzXHJcbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgICA8bGk+XHJcbiAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9qb3VybmFsc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXdoaXRlXCJcclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIEpvdXJuYWxzXHJcbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgICA8bGk+XHJcbiAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9hcnRpY2xlc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXdoaXRlXCJcclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIEFydGljbGVzXHJcbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgICA8bGk+XHJcbiAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXdoaXRlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgUHJpdmFjeSBQb2xpY3lcclxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgPC9saT5cclxuICAgICAgICAgICAgICA8L3VsPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZCBtYi00XCI+U2VydmljZXM8L2gzPlxyXG4gICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0xXCI+XHJcbiAgICAgICAgICAgICAgPGxpPlxyXG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIiNcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtd2hpdGVcIj5cclxuICAgICAgICAgICAgICAgICAgUHVibGlzaGluZ1xyXG4gICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgPGxpPlxyXG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIiNcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtd2hpdGVcIj5cclxuICAgICAgICAgICAgICAgICAgRWRpdGluZ1xyXG4gICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgPGxpPlxyXG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIiNcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtd2hpdGVcIj5cclxuICAgICAgICAgICAgICAgICAgRm9ybWF0dGluZ1xyXG4gICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgIDwvdWw+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNlwiPlxyXG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgbWItNFwiPk5ld3NsZXR0ZXIgU2lnbiBVcDwvaDM+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleFwiPlxyXG4gICAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgICAgdHlwZT1cImVtYWlsXCJcclxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgeW91ciBFbWFpbFwiXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmF5LTYwMCBweC00IHB5LTIgdy1mdWxsIHJvdW5kZWRcIlxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJiZy13aGl0ZSB0ZXh0LWdyYXktOTAwIG1zLTIgcHgtNCBweS0yIHJvdW5kZWRcIj5cclxuICAgICAgICAgICAgICAgIFN1Ym1pdFxyXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtOCBwdC04IGJvcmRlci10IGJvcmRlci1ncmF5LTcwMCB0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPkNvcHlyaWdodCDCqSAyMDI1IC0gVGluYSBFZHVjYXRpb248L3A+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9mb290ZXI+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiTGluayIsIkZvb3RlciIsImZvb3RlciIsImNsYXNzTmFtZSIsImRpdiIsImgzIiwidWwiLCJsaSIsImhyZWYiLCJpbnB1dCIsInR5cGUiLCJwbGFjZWhvbGRlciIsImJ1dHRvbiIsInAiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/components/footer.tsx\n");

/***/ }),

/***/ "(rsc)/./app/components/home/<USER>":
/*!******************************************************!*\
  !*** ./app/components/home/<USER>
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturedPublications)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../prisma */ \"(rsc)/./prisma.ts\");\n/* harmony import */ var _PublicationCarousel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./PublicationCarousel */ \"(rsc)/./app/components/home/<USER>");\n\n\n\nasync function getFeaturedPublications() {\n    try {\n        // Fetch books and ebooks\n        const books = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.publication.findMany({\n            where: {\n                type: {\n                    in: [\n                        \"BOOK\",\n                        \"EBOOK\",\n                        \"AUDIOBOOK\"\n                    ]\n                }\n            },\n            include: {\n                user: {\n                    select: {\n                        name: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            },\n            take: 6\n        });\n        // Fetch journals\n        const journals = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.publication.findMany({\n            where: {\n                type: \"JOURNAL\"\n            },\n            include: {\n                user: {\n                    select: {\n                        name: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            },\n            take: 6\n        });\n        // Fetch articles\n        const articles = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.publication.findMany({\n            where: {\n                type: \"ARTICLE\"\n            },\n            include: {\n                user: {\n                    select: {\n                        name: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            },\n            take: 6\n        });\n        return {\n            books,\n            journals,\n            articles\n        };\n    } catch (error) {\n        console.error(\"Error fetching featured publications:\", error);\n        return {\n            books: [],\n            journals: [],\n            articles: []\n        };\n    }\n}\nasync function FeaturedPublications() {\n    const { books, journals, articles } = await getFeaturedPublications();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"books\",\n        className: \"py-16 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto w-[90%] max-w-7xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-4\",\n                            children: \"Featured Publications\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\FeaturedPublications.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-400\",\n                            children: \"Discover the latest academic works and research from our community\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\FeaturedPublications.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\FeaturedPublications.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PublicationCarousel__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    title: \"Books & eBooks\",\n                    type: \"books\",\n                    publications: books\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\FeaturedPublications.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PublicationCarousel__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    title: \"Journals\",\n                    type: \"journals\",\n                    publications: journals\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\FeaturedPublications.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PublicationCarousel__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    title: \"Articles\",\n                    type: \"articles\",\n                    publications: articles\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\FeaturedPublications.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row justify-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/books\",\n                                className: \"inline-flex items-center px-8 py-3 bg-blue-900 text-white rounded-lg hover:bg-blue-800 transition-colors font-medium\",\n                                children: [\n                                    \"Browse All Books\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2\",\n                                        children: \"→\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\FeaturedPublications.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\FeaturedPublications.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/journals\",\n                                className: \"inline-flex items-center px-8 py-3 bg-blue-700 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium\",\n                                children: [\n                                    \"Browse All Journals\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2\",\n                                        children: \"→\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\FeaturedPublications.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\FeaturedPublications.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/articles\",\n                                className: \"inline-flex items-center px-8 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors font-medium\",\n                                children: [\n                                    \"Browse All Articles\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2\",\n                                        children: \"→\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\FeaturedPublications.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\FeaturedPublications.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\FeaturedPublications.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\FeaturedPublications.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\FeaturedPublications.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\FeaturedPublications.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/components/home/<USER>");

/***/ }),

/***/ "(rsc)/./app/components/home/<USER>":
/*!*****************************************************!*\
  !*** ./app/components/home/<USER>
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\home\\PublicationCarousel.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/components/home_nav.tsx":
/*!*************************************!*\
  !*** ./app/components/home_nav.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\home_nav.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/context/AuthProvider.tsx":
/*!**************************************!*\
  !*** ./app/context/AuthProvider.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\context\\\\AuthProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\context\\AuthProvider.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"249b9d197b61\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmljaGVcXFRpbmEgRWR1Y2F0aW9uLm9yZ1xcUmV2aWV3IFJlcXVlc3QgYmFja2VuZFxcQVJSUy1OZXh0SlNcXHRpbmEtZWR1Y2F0aW9uXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMjQ5YjlkMTk3YjYxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _context_AuthProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context/AuthProvider */ \"(rsc)/./app/context/AuthProvider.tsx\");\n/* harmony import */ var _components_home_nav__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/home_nav */ \"(rsc)/./app/components/home_nav.tsx\");\n/* harmony import */ var _components_footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/footer */ \"(rsc)/./app/components/footer.tsx\");\n/* harmony import */ var _components_NotificationToast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/NotificationToast */ \"(rsc)/./app/components/NotificationToast.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Tina Education\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_6___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_7___default().variable)} antialiased min-h-screen flex flex-col`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthProvider__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_nav__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NotificationToast__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_home_FeaturedPublications__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/home/<USER>/ \"(rsc)/./app/components/home/<USER>");\n/* harmony import */ var _components_Search__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/Search */ \"(rsc)/./app/components/Search.tsx\");\n\n\n\n\n\nconst Home = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-100 min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b mt-18 border-gray-200 shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto w-[90%] max-w-7xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"flex py-4 space-x-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/books\",\n                                    className: \"text-gray-800 font-medium hover:text-blue-900 transition-colors\",\n                                    children: \"Books\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/articles\",\n                                    className: \"text-gray-800 font-medium hover:text-blue-900 transition-colors\",\n                                    children: \"Articles\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/journals\",\n                                    className: \"text-gray-800 font-medium hover:text-blue-900 transition-colors\",\n                                    children: \"Journals\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-blue-900 text-white py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto w-[90%] max-w-7xl text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl md:text-5xl font-bold mb-6\",\n                            children: \"Enhance the Author in You\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl mb-10 text-blue-100 max-w-2xl mx-auto\",\n                            children: \"Access eBooks, Journals, and professional tools to elevate your writing craft and connect with the academic community.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-2xl mx-auto mb-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Search__WEBPACK_IMPORTED_MODULE_4__.SearchBox, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row justify-center gap-4 mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/dashboard\",\n                                    className: \"px-8 py-3 border-2 border-white text-white rounded-lg font-medium hover:bg-white hover:text-blue-900 transition-colors\",\n                                    children: \"Get Started\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"#journals\",\n                                    className: \"px-8 py-3 bg-white text-blue-900 rounded-lg font-medium hover:bg-gray-100 transition-colors shadow-lg\",\n                                    children: \"Explore Journals\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"#books\",\n                                    className: \"px-8 py-3 border-2 border-white text-white rounded-lg font-medium hover:bg-white hover:text-blue-900 transition-colors\",\n                                    children: \"Explore eBooks\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto w-[90%] max-w-7xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"Why Choose Tina Education?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                                    children: \"Discover the tools and resources that make academic publishing and collaboration seamless.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-8 hover:shadow-md transition-shadow cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-blue-50 text-blue-900 rounded-lg flex items-center justify-center text-2xl mb-6\",\n                                            children: \"\\uD83D\\uDCDA\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-900 mb-4\",\n                                            children: \"Custom eBook Library\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Build and manage your personal digital library with advanced organization tools and seamless access across devices.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-8 hover:shadow-md transition-shadow cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-green-50 text-green-900 rounded-lg flex items-center justify-center text-2xl mb-6\",\n                                            children: \"\\uD83D\\uDCCB\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-900 mb-4\",\n                                            children: \"Peer-to-Peer Review Process\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Engage in collaborative academic review with structured feedback systems and transparent evaluation processes.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-8 hover:shadow-md transition-shadow cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-purple-50 text-purple-900 rounded-lg flex items-center justify-center text-2xl mb-6\",\n                                            children: \"✍️\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-900 mb-4\",\n                                            children: \"Writing Resources\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Access comprehensive writing tools, templates, and guidelines to enhance your academic writing and publication success.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_FeaturedPublications__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto w-[90%] max-w-7xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"Ready to Start Your Academic Journey?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                                    children: \"Join our community of researchers, authors, and academics. Share your work, collaborate with peers, and advance your career.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-12 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-blue-50 text-blue-900 rounded-lg flex items-center justify-center text-3xl mb-6 mx-auto\",\n                                            children: \"\\uD83D\\uDE80\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-900 mb-4 text-center\",\n                                            children: \"For Authors & Researchers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3 text-gray-600 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-500 mr-3\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Publish your research and manuscripts\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-500 mr-3\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Access peer review networks\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-500 mr-3\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Build your academic profile\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-500 mr-3\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Connect with the academic community\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/dashboard\",\n                                                className: \"inline-flex items-center px-6 py-3 bg-blue-900 text-white rounded-lg hover:bg-blue-800 transition-colors font-medium\",\n                                                children: [\n                                                    \"Start Publishing\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2\",\n                                                        children: \"→\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-green-50 text-green-900 rounded-lg flex items-center justify-center text-3xl mb-6 mx-auto\",\n                                            children: \"\\uD83D\\uDCD6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-900 mb-4 text-center\",\n                                            children: \"For Readers & Reviewers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3 text-gray-600 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-500 mr-3\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Access extensive academic library\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-500 mr-3\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Participate in peer review process\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-500 mr-3\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Discover latest research\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-500 mr-3\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Support academic excellence\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/dashboard/reviews\",\n                                                className: \"inline-flex items-center px-6 py-3 bg-blue-900 text-white rounded-lg hover:bg-blue-800 transition-colors font-medium\",\n                                                children: [\n                                                    \"Start Reviewing\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2\",\n                                                        children: \"→\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Home);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome%5C%5CPublicationCarousel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CSearch.tsx%22%2C%22ids%22%3A%5B%22SearchBox%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome%5C%5CPublicationCarousel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CSearch.tsx%22%2C%22ids%22%3A%5B%22SearchBox%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/home/<USER>/ \"(rsc)/./app/components/home/<USER>"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/Search.tsx */ \"(rsc)/./app/components/Search.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome%5C%5CPublicationCarousel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CSearch.tsx%22%2C%22ids%22%3A%5B%22SearchBox%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome_nav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CNotificationToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccontext%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome_nav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CNotificationToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccontext%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/home_nav.tsx */ \"(rsc)/./app/components/home_nav.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/NotificationToast.tsx */ \"(rsc)/./app/components/NotificationToast.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/AuthProvider.tsx */ \"(rsc)/./app/context/AuthProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome_nav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CNotificationToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccontext%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./prisma.ts":
/*!*******************!*\
  !*** ./prisma.ts ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9wcmlzbWEudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThDO0FBRTlDLE1BQU1DLGtCQUFrQkM7QUFFakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUc7QUFFbkUsSUFBSUksSUFBcUMsRUFBRUgsZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJpY2hlXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXEFSUlMtTmV4dEpTXFx0aW5hLWVkdWNhdGlvblxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gXCJAcHJpc21hL2NsaWVudFwiO1xyXG5cclxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHsgcHJpc21hOiBQcmlzbWFDbGllbnQgfTtcclxuXHJcbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hIHx8IG5ldyBQcmlzbWFDbGllbnQoKTtcclxuXHJcbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./prisma.ts\n");

/***/ }),

/***/ "(ssr)/./app/components/NotificationBell.tsx":
/*!*********************************************!*\
  !*** ./app/components/NotificationBell.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationBell: () => (/* binding */ NotificationBell)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FaBell_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FaBell!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ NotificationBell auto */ \n\n\n\n\nfunction NotificationBell() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            // Wait for session to be fully loaded and authenticated\n            if (status === \"loading\" || !session?.user?.id) {\n                return;\n            }\n            const fetchUnreadCount = {\n                \"NotificationBell.useEffect.fetchUnreadCount\": async ()=>{\n                    try {\n                        const response = await fetch(\"/api/auth/notifications\", {\n                            headers: {\n                                \"Cache-Control\": \"no-cache\"\n                            }\n                        });\n                        if (response.ok) {\n                            const data = await response.json();\n                            setUnreadCount(data.unreadCount || 0);\n                        } else if (response.status === 401) {\n                            // Unauthorized - session might have expired\n                            setUnreadCount(0);\n                        } else {\n                            // Other errors - keep previous count\n                            console.warn(\"Failed to fetch notification count:\", response.status, response.statusText);\n                        }\n                    } catch (error) {\n                        // Silently handle network errors to avoid console spam\n                        if (error instanceof TypeError && error.message.includes(\"fetch\")) {\n                            // Network error - keep previous count\n                            return;\n                        }\n                        console.error(\"Failed to fetch notification count:\", error);\n                    }\n                }\n            }[\"NotificationBell.useEffect.fetchUnreadCount\"];\n            // Initial fetch with a small delay to ensure session is stable\n            const timeoutId = setTimeout(fetchUnreadCount, 100);\n            // Poll for updates every 30 seconds\n            const interval = setInterval(fetchUnreadCount, 30000);\n            return ({\n                \"NotificationBell.useEffect\": ()=>{\n                    clearTimeout(timeoutId);\n                    clearInterval(interval);\n                }\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], [\n        session?.user?.id,\n        status\n    ]);\n    // Wait for session to load\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-6 h-6 animate-pulse bg-gray-200 rounded\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationBell.tsx\",\n            lineNumber: 64,\n            columnNumber: 12\n        }, this);\n    }\n    // No session means user is not logged in\n    if (status === \"unauthenticated\" || !session?.user?.id) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n        href: \"/dashboard/notifications\",\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__.FaBell, {\n                size: 24,\n                className: \"text-gray-600 hover:text-blue-600 transition-colors\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationBell.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse\",\n                children: unreadCount > 99 ? \"99+\" : unreadCount\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationBell.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationBell.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/NotificationBell.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/NotificationToast.tsx":
/*!**********************************************!*\
  !*** ./app/components/NotificationToast.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction NotificationToast() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const removeNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationToast.useCallback[removeNotification]\": (id)=>{\n            setNotifications({\n                \"NotificationToast.useCallback[removeNotification]\": (prev)=>prev.filter({\n                        \"NotificationToast.useCallback[removeNotification]\": (n)=>n.id !== id\n                    }[\"NotificationToast.useCallback[removeNotification]\"])\n            }[\"NotificationToast.useCallback[removeNotification]\"]);\n        }\n    }[\"NotificationToast.useCallback[removeNotification]\"], []);\n    // Function to add a new toast notification\n    const addNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationToast.useCallback[addNotification]\": (notification)=>{\n            const id = `toast_${Date.now()}_${Math.random().toString(36).substring(7)}`;\n            const newNotification = {\n                ...notification,\n                id\n            };\n            setNotifications({\n                \"NotificationToast.useCallback[addNotification]\": (prev)=>[\n                        ...prev,\n                        newNotification\n                    ]\n            }[\"NotificationToast.useCallback[addNotification]\"]);\n            // Auto-remove after 5 seconds\n            setTimeout({\n                \"NotificationToast.useCallback[addNotification]\": ()=>{\n                    removeNotification(id);\n                }\n            }[\"NotificationToast.useCallback[addNotification]\"], 5000);\n        }\n    }[\"NotificationToast.useCallback[addNotification]\"], [\n        removeNotification\n    ]);\n    // Poll for new notifications (in a real app, you'd use WebSockets or Server-Sent Events)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationToast.useEffect\": ()=>{\n            // Wait for session to be fully loaded and authenticated\n            if (status === \"loading\" || !session?.user?.id) return;\n            const checkForNewNotifications = {\n                \"NotificationToast.useEffect.checkForNewNotifications\": async ()=>{\n                    try {\n                        const response = await fetch(\"/api/auth/notifications\", {\n                            headers: {\n                                \"Cache-Control\": \"no-cache\"\n                            }\n                        });\n                        if (response.ok) {\n                            await response.json();\n                        // This is a simple implementation - in production you'd want to track which notifications are new\n                        // and only show toasts for truly new ones\n                        } else if (response.status === 401) {\n                            // Unauthorized - session might have expired, stop polling\n                            return;\n                        } else {\n                            console.warn(\"Failed to fetch notifications:\", response.status, response.statusText);\n                        }\n                    } catch (error) {\n                        // Silently handle network errors to avoid spamming console\n                        // Only log if it's not a network connectivity issue\n                        if (error instanceof TypeError && error.message.includes(\"fetch\")) {\n                            // Network error - don't log to avoid spam\n                            return;\n                        }\n                        console.error(\"Failed to check for notifications:\", error);\n                    }\n                }\n            }[\"NotificationToast.useEffect.checkForNewNotifications\"];\n            // Initial delay to ensure session is stable, then check every 30 seconds\n            const timeoutId = setTimeout({\n                \"NotificationToast.useEffect.timeoutId\": ()=>{\n                    checkForNewNotifications(); // Initial check\n                }\n            }[\"NotificationToast.useEffect.timeoutId\"], 1000);\n            const interval = setInterval(checkForNewNotifications, 30000);\n            return ({\n                \"NotificationToast.useEffect\": ()=>{\n                    clearTimeout(timeoutId);\n                    clearInterval(interval);\n                }\n            })[\"NotificationToast.useEffect\"];\n        }\n    }[\"NotificationToast.useEffect\"], [\n        session?.user?.id,\n        status\n    ]);\n    // Expose the addNotification function globally for other components to use\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationToast.useEffect\": ()=>{\n            window.showNotificationToast = addNotification;\n            return ({\n                \"NotificationToast.useEffect\": ()=>{\n                    delete window.showNotificationToast;\n                }\n            })[\"NotificationToast.useEffect\"];\n        }\n    }[\"NotificationToast.useEffect\"], [\n        addNotification\n    ]);\n    const getToastStyles = (type)=>{\n        const baseStyles = \"p-4 rounded-lg shadow-lg border-l-4 max-w-sm\";\n        switch(type){\n            case \"success\":\n                return `${baseStyles} bg-green-50 border-green-400 text-green-800`;\n            case \"error\":\n                return `${baseStyles} bg-red-50 border-red-400 text-red-800`;\n            case \"warning\":\n                return `${baseStyles} bg-yellow-50 border-yellow-400 text-yellow-800`;\n            case \"info\":\n            default:\n                return `${baseStyles} bg-blue-50 border-blue-400 text-blue-800`;\n        }\n    };\n    const getIcon = (type)=>{\n        switch(type){\n            case \"success\":\n                return \"✅\";\n            case \"error\":\n                return \"❌\";\n            case \"warning\":\n                return \"⚠️\";\n            case \"info\":\n            default:\n                return \"ℹ️\";\n        }\n    };\n    if (notifications.length === 0) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 space-y-2\",\n        children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${getToastStyles(notification.type)} animate-slide-in-right`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg mr-3\",\n                            children: getIcon(notification.type)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium\",\n                                    children: notification.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm mt-1\",\n                                    children: notification.message\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>removeNotification(notification.id),\n                            className: \"ml-3 text-gray-400 hover:text-gray-600\",\n                            children: \"\\xd7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 11\n                }, this)\n            }, notification.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9Ob3RpZmljYXRpb25Ub2FzdC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUV5RDtBQUNaO0FBUzlCLFNBQVNJO0lBQ3RCLE1BQU0sRUFBRUMsTUFBTUMsT0FBTyxFQUFFQyxNQUFNLEVBQUUsR0FBR0osMkRBQVVBO0lBQzVDLE1BQU0sQ0FBQ0ssZUFBZUMsaUJBQWlCLEdBQUdSLCtDQUFRQSxDQUFzQixFQUFFO0lBRTFFLE1BQU1TLHFCQUFxQlIsa0RBQVdBOzZEQUFDLENBQUNTO1lBQ3RDRjtxRUFBaUIsQ0FBQ0csT0FBU0EsS0FBS0MsTUFBTTs2RUFBQyxDQUFDQyxJQUFNQSxFQUFFSCxFQUFFLEtBQUtBOzs7UUFDekQ7NERBQUcsRUFBRTtJQUVMLDJDQUEyQztJQUMzQyxNQUFNSSxrQkFBa0JiLGtEQUFXQTswREFDakMsQ0FBQ2M7WUFDQyxNQUFNTCxLQUFLLENBQUMsTUFBTSxFQUFFTSxLQUFLQyxHQUFHLEdBQUcsQ0FBQyxFQUFFQyxLQUFLQyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxTQUFTLENBQUMsSUFBSTtZQUMzRSxNQUFNQyxrQkFBa0I7Z0JBQUUsR0FBR1AsWUFBWTtnQkFBRUw7WUFBRztZQUU5Q0Y7a0VBQWlCLENBQUNHLE9BQVM7MkJBQUlBO3dCQUFNVztxQkFBZ0I7O1lBRXJELDhCQUE4QjtZQUM5QkM7a0VBQVc7b0JBQ1RkLG1CQUFtQkM7Z0JBQ3JCO2lFQUFHO1FBQ0w7eURBQ0E7UUFBQ0Q7S0FBbUI7SUFHdEIseUZBQXlGO0lBQ3pGVixnREFBU0E7dUNBQUM7WUFDUix3REFBd0Q7WUFDeEQsSUFBSU8sV0FBVyxhQUFhLENBQUNELFNBQVNtQixNQUFNZCxJQUFJO1lBRWhELE1BQU1lO3dFQUEyQjtvQkFDL0IsSUFBSTt3QkFDRixNQUFNQyxXQUFXLE1BQU1DLE1BQU0sMkJBQTJCOzRCQUN0REMsU0FBUztnQ0FDUCxpQkFBaUI7NEJBQ25CO3dCQUNGO3dCQUVBLElBQUlGLFNBQVNHLEVBQUUsRUFBRTs0QkFDZixNQUFNSCxTQUFTSSxJQUFJO3dCQUNuQixrR0FBa0c7d0JBQ2xHLDBDQUEwQzt3QkFDNUMsT0FBTyxJQUFJSixTQUFTcEIsTUFBTSxLQUFLLEtBQUs7NEJBQ2xDLDBEQUEwRDs0QkFDMUQ7d0JBQ0YsT0FBTzs0QkFDTHlCLFFBQVFDLElBQUksQ0FDVixrQ0FDQU4sU0FBU3BCLE1BQU0sRUFDZm9CLFNBQVNPLFVBQVU7d0JBRXZCO29CQUNGLEVBQUUsT0FBT0MsT0FBTzt3QkFDZCwyREFBMkQ7d0JBQzNELG9EQUFvRDt3QkFDcEQsSUFBSUEsaUJBQWlCQyxhQUFhRCxNQUFNRSxPQUFPLENBQUNDLFFBQVEsQ0FBQyxVQUFVOzRCQUNqRSwwQ0FBMEM7NEJBQzFDO3dCQUNGO3dCQUNBTixRQUFRRyxLQUFLLENBQUMsc0NBQXNDQTtvQkFDdEQ7Z0JBQ0Y7O1lBRUEseUVBQXlFO1lBQ3pFLE1BQU1JLFlBQVlmO3lEQUFXO29CQUMzQkUsNEJBQTRCLGdCQUFnQjtnQkFDOUM7d0RBQUc7WUFFSCxNQUFNYyxXQUFXQyxZQUFZZiwwQkFBMEI7WUFFdkQ7K0NBQU87b0JBQ0xnQixhQUFhSDtvQkFDYkksY0FBY0g7Z0JBQ2hCOztRQUNGO3NDQUFHO1FBQUNsQyxTQUFTbUIsTUFBTWQ7UUFBSUo7S0FBTztJQUU5QiwyRUFBMkU7SUFDM0VQLGdEQUFTQTt1Q0FBQztZQUVONEMsT0FDQUMscUJBQXFCLEdBQUc5QjtZQUUxQjsrQ0FBTztvQkFDTCxPQUFPLE9BRUw4QixxQkFBcUI7Z0JBQ3pCOztRQUNGO3NDQUFHO1FBQUM5QjtLQUFnQjtJQUVwQixNQUFNK0IsaUJBQWlCLENBQUNDO1FBQ3RCLE1BQU1DLGFBQWE7UUFFbkIsT0FBUUQ7WUFDTixLQUFLO2dCQUNILE9BQU8sR0FBR0MsV0FBVyw0Q0FBNEMsQ0FBQztZQUNwRSxLQUFLO2dCQUNILE9BQU8sR0FBR0EsV0FBVyxzQ0FBc0MsQ0FBQztZQUM5RCxLQUFLO2dCQUNILE9BQU8sR0FBR0EsV0FBVywrQ0FBK0MsQ0FBQztZQUN2RSxLQUFLO1lBQ0w7Z0JBQ0UsT0FBTyxHQUFHQSxXQUFXLHlDQUF5QyxDQUFDO1FBQ25FO0lBQ0Y7SUFFQSxNQUFNQyxVQUFVLENBQUNGO1FBQ2YsT0FBUUE7WUFDTixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO1lBQ0w7Z0JBQ0UsT0FBTztRQUNYO0lBQ0Y7SUFFQSxJQUFJdkMsY0FBYzBDLE1BQU0sS0FBSyxHQUFHLE9BQU87SUFFdkMscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7a0JBQ1o1QyxjQUFjNkMsR0FBRyxDQUFDLENBQUNyQyw2QkFDbEIsOERBQUNtQztnQkFFQ0MsV0FBVyxHQUFHTixlQUFlOUIsYUFBYStCLElBQUksRUFBRSx1QkFBdUIsQ0FBQzswQkFFeEUsNEVBQUNJO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0U7NEJBQUtGLFdBQVU7c0NBQWdCSCxRQUFRakMsYUFBYStCLElBQUk7Ozs7OztzQ0FDekQsOERBQUNJOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0c7b0NBQUdILFdBQVU7OENBQWVwQyxhQUFhd0MsS0FBSzs7Ozs7OzhDQUMvQyw4REFBQ0M7b0NBQUVMLFdBQVU7OENBQWdCcEMsYUFBYXFCLE9BQU87Ozs7Ozs7Ozs7OztzQ0FFbkQsOERBQUNxQjs0QkFDQ0MsU0FBUyxJQUFNakQsbUJBQW1CTSxhQUFhTCxFQUFFOzRCQUNqRHlDLFdBQVU7c0NBQ1g7Ozs7Ozs7Ozs7OztlQVpFcEMsYUFBYUwsRUFBRTs7Ozs7Ozs7OztBQW9COUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmljaGVcXFRpbmEgRWR1Y2F0aW9uLm9yZ1xcUmV2aWV3IFJlcXVlc3QgYmFja2VuZFxcQVJSUy1OZXh0SlNcXHRpbmEtZWR1Y2F0aW9uXFxhcHBcXGNvbXBvbmVudHNcXE5vdGlmaWNhdGlvblRvYXN0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSwgdXNlQ2FsbGJhY2sgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IHVzZVNlc3Npb24gfSBmcm9tIFwibmV4dC1hdXRoL3JlYWN0XCI7XG5cbmludGVyZmFjZSBUb2FzdE5vdGlmaWNhdGlvbiB7XG4gIGlkOiBzdHJpbmc7XG4gIHRpdGxlOiBzdHJpbmc7XG4gIG1lc3NhZ2U6IHN0cmluZztcbiAgdHlwZTogXCJzdWNjZXNzXCIgfCBcImluZm9cIiB8IFwid2FybmluZ1wiIHwgXCJlcnJvclwiO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOb3RpZmljYXRpb25Ub2FzdCgpIHtcbiAgY29uc3QgeyBkYXRhOiBzZXNzaW9uLCBzdGF0dXMgfSA9IHVzZVNlc3Npb24oKTtcbiAgY29uc3QgW25vdGlmaWNhdGlvbnMsIHNldE5vdGlmaWNhdGlvbnNdID0gdXNlU3RhdGU8VG9hc3ROb3RpZmljYXRpb25bXT4oW10pO1xuXG4gIGNvbnN0IHJlbW92ZU5vdGlmaWNhdGlvbiA9IHVzZUNhbGxiYWNrKChpZDogc3RyaW5nKSA9PiB7XG4gICAgc2V0Tm90aWZpY2F0aW9ucygocHJldikgPT4gcHJldi5maWx0ZXIoKG4pID0+IG4uaWQgIT09IGlkKSk7XG4gIH0sIFtdKTtcblxuICAvLyBGdW5jdGlvbiB0byBhZGQgYSBuZXcgdG9hc3Qgbm90aWZpY2F0aW9uXG4gIGNvbnN0IGFkZE5vdGlmaWNhdGlvbiA9IHVzZUNhbGxiYWNrKFxuICAgIChub3RpZmljYXRpb246IE9taXQ8VG9hc3ROb3RpZmljYXRpb24sIFwiaWRcIj4pID0+IHtcbiAgICAgIGNvbnN0IGlkID0gYHRvYXN0XyR7RGF0ZS5ub3coKX1fJHtNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHJpbmcoNyl9YDtcbiAgICAgIGNvbnN0IG5ld05vdGlmaWNhdGlvbiA9IHsgLi4ubm90aWZpY2F0aW9uLCBpZCB9O1xuXG4gICAgICBzZXROb3RpZmljYXRpb25zKChwcmV2KSA9PiBbLi4ucHJldiwgbmV3Tm90aWZpY2F0aW9uXSk7XG5cbiAgICAgIC8vIEF1dG8tcmVtb3ZlIGFmdGVyIDUgc2Vjb25kc1xuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIHJlbW92ZU5vdGlmaWNhdGlvbihpZCk7XG4gICAgICB9LCA1MDAwKTtcbiAgICB9LFxuICAgIFtyZW1vdmVOb3RpZmljYXRpb25dXG4gICk7XG5cbiAgLy8gUG9sbCBmb3IgbmV3IG5vdGlmaWNhdGlvbnMgKGluIGEgcmVhbCBhcHAsIHlvdSdkIHVzZSBXZWJTb2NrZXRzIG9yIFNlcnZlci1TZW50IEV2ZW50cylcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBXYWl0IGZvciBzZXNzaW9uIHRvIGJlIGZ1bGx5IGxvYWRlZCBhbmQgYXV0aGVudGljYXRlZFxuICAgIGlmIChzdGF0dXMgPT09IFwibG9hZGluZ1wiIHx8ICFzZXNzaW9uPy51c2VyPy5pZCkgcmV0dXJuO1xuXG4gICAgY29uc3QgY2hlY2tGb3JOZXdOb3RpZmljYXRpb25zID0gYXN5bmMgKCkgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChcIi9hcGkvYXV0aC9ub3RpZmljYXRpb25zXCIsIHtcbiAgICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgICBcIkNhY2hlLUNvbnRyb2xcIjogXCJuby1jYWNoZVwiLFxuICAgICAgICAgIH0sXG4gICAgICAgIH0pO1xuXG4gICAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICAgIGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgICAvLyBUaGlzIGlzIGEgc2ltcGxlIGltcGxlbWVudGF0aW9uIC0gaW4gcHJvZHVjdGlvbiB5b3UnZCB3YW50IHRvIHRyYWNrIHdoaWNoIG5vdGlmaWNhdGlvbnMgYXJlIG5ld1xuICAgICAgICAgIC8vIGFuZCBvbmx5IHNob3cgdG9hc3RzIGZvciB0cnVseSBuZXcgb25lc1xuICAgICAgICB9IGVsc2UgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gNDAxKSB7XG4gICAgICAgICAgLy8gVW5hdXRob3JpemVkIC0gc2Vzc2lvbiBtaWdodCBoYXZlIGV4cGlyZWQsIHN0b3AgcG9sbGluZ1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oXG4gICAgICAgICAgICBcIkZhaWxlZCB0byBmZXRjaCBub3RpZmljYXRpb25zOlwiLFxuICAgICAgICAgICAgcmVzcG9uc2Uuc3RhdHVzLFxuICAgICAgICAgICAgcmVzcG9uc2Uuc3RhdHVzVGV4dFxuICAgICAgICAgICk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIC8vIFNpbGVudGx5IGhhbmRsZSBuZXR3b3JrIGVycm9ycyB0byBhdm9pZCBzcGFtbWluZyBjb25zb2xlXG4gICAgICAgIC8vIE9ubHkgbG9nIGlmIGl0J3Mgbm90IGEgbmV0d29yayBjb25uZWN0aXZpdHkgaXNzdWVcbiAgICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgVHlwZUVycm9yICYmIGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoXCJmZXRjaFwiKSkge1xuICAgICAgICAgIC8vIE5ldHdvcmsgZXJyb3IgLSBkb24ndCBsb2cgdG8gYXZvaWQgc3BhbVxuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBjb25zb2xlLmVycm9yKFwiRmFpbGVkIHRvIGNoZWNrIGZvciBub3RpZmljYXRpb25zOlwiLCBlcnJvcik7XG4gICAgICB9XG4gICAgfTtcblxuICAgIC8vIEluaXRpYWwgZGVsYXkgdG8gZW5zdXJlIHNlc3Npb24gaXMgc3RhYmxlLCB0aGVuIGNoZWNrIGV2ZXJ5IDMwIHNlY29uZHNcbiAgICBjb25zdCB0aW1lb3V0SWQgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIGNoZWNrRm9yTmV3Tm90aWZpY2F0aW9ucygpOyAvLyBJbml0aWFsIGNoZWNrXG4gICAgfSwgMTAwMCk7XG5cbiAgICBjb25zdCBpbnRlcnZhbCA9IHNldEludGVydmFsKGNoZWNrRm9yTmV3Tm90aWZpY2F0aW9ucywgMzAwMDApO1xuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xuICAgICAgY2xlYXJJbnRlcnZhbChpbnRlcnZhbCk7XG4gICAgfTtcbiAgfSwgW3Nlc3Npb24/LnVzZXI/LmlkLCBzdGF0dXNdKTtcblxuICAvLyBFeHBvc2UgdGhlIGFkZE5vdGlmaWNhdGlvbiBmdW5jdGlvbiBnbG9iYWxseSBmb3Igb3RoZXIgY29tcG9uZW50cyB0byB1c2VcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAoXG4gICAgICB3aW5kb3cgYXMgV2luZG93ICYgeyBzaG93Tm90aWZpY2F0aW9uVG9hc3Q/OiB0eXBlb2YgYWRkTm90aWZpY2F0aW9uIH1cbiAgICApLnNob3dOb3RpZmljYXRpb25Ub2FzdCA9IGFkZE5vdGlmaWNhdGlvbjtcblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBkZWxldGUgKFxuICAgICAgICB3aW5kb3cgYXMgV2luZG93ICYgeyBzaG93Tm90aWZpY2F0aW9uVG9hc3Q/OiB0eXBlb2YgYWRkTm90aWZpY2F0aW9uIH1cbiAgICAgICkuc2hvd05vdGlmaWNhdGlvblRvYXN0O1xuICAgIH07XG4gIH0sIFthZGROb3RpZmljYXRpb25dKTtcblxuICBjb25zdCBnZXRUb2FzdFN0eWxlcyA9ICh0eXBlOiBUb2FzdE5vdGlmaWNhdGlvbltcInR5cGVcIl0pID0+IHtcbiAgICBjb25zdCBiYXNlU3R5bGVzID0gXCJwLTQgcm91bmRlZC1sZyBzaGFkb3ctbGcgYm9yZGVyLWwtNCBtYXgtdy1zbVwiO1xuXG4gICAgc3dpdGNoICh0eXBlKSB7XG4gICAgICBjYXNlIFwic3VjY2Vzc1wiOlxuICAgICAgICByZXR1cm4gYCR7YmFzZVN0eWxlc30gYmctZ3JlZW4tNTAgYm9yZGVyLWdyZWVuLTQwMCB0ZXh0LWdyZWVuLTgwMGA7XG4gICAgICBjYXNlIFwiZXJyb3JcIjpcbiAgICAgICAgcmV0dXJuIGAke2Jhc2VTdHlsZXN9IGJnLXJlZC01MCBib3JkZXItcmVkLTQwMCB0ZXh0LXJlZC04MDBgO1xuICAgICAgY2FzZSBcIndhcm5pbmdcIjpcbiAgICAgICAgcmV0dXJuIGAke2Jhc2VTdHlsZXN9IGJnLXllbGxvdy01MCBib3JkZXIteWVsbG93LTQwMCB0ZXh0LXllbGxvdy04MDBgO1xuICAgICAgY2FzZSBcImluZm9cIjpcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiBgJHtiYXNlU3R5bGVzfSBiZy1ibHVlLTUwIGJvcmRlci1ibHVlLTQwMCB0ZXh0LWJsdWUtODAwYDtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZ2V0SWNvbiA9ICh0eXBlOiBUb2FzdE5vdGlmaWNhdGlvbltcInR5cGVcIl0pID0+IHtcbiAgICBzd2l0Y2ggKHR5cGUpIHtcbiAgICAgIGNhc2UgXCJzdWNjZXNzXCI6XG4gICAgICAgIHJldHVybiBcIuKchVwiO1xuICAgICAgY2FzZSBcImVycm9yXCI6XG4gICAgICAgIHJldHVybiBcIuKdjFwiO1xuICAgICAgY2FzZSBcIndhcm5pbmdcIjpcbiAgICAgICAgcmV0dXJuIFwi4pqg77iPXCI7XG4gICAgICBjYXNlIFwiaW5mb1wiOlxuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuIFwi4oS577iPXCI7XG4gICAgfVxuICB9O1xuXG4gIGlmIChub3RpZmljYXRpb25zLmxlbmd0aCA9PT0gMCkgcmV0dXJuIG51bGw7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIHRvcC00IHJpZ2h0LTQgei01MCBzcGFjZS15LTJcIj5cbiAgICAgIHtub3RpZmljYXRpb25zLm1hcCgobm90aWZpY2F0aW9uKSA9PiAoXG4gICAgICAgIDxkaXZcbiAgICAgICAgICBrZXk9e25vdGlmaWNhdGlvbi5pZH1cbiAgICAgICAgICBjbGFzc05hbWU9e2Ake2dldFRvYXN0U3R5bGVzKG5vdGlmaWNhdGlvbi50eXBlKX0gYW5pbWF0ZS1zbGlkZS1pbi1yaWdodGB9XG4gICAgICAgID5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnRcIj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGcgbXItM1wiPntnZXRJY29uKG5vdGlmaWNhdGlvbi50eXBlKX08L3NwYW4+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57bm90aWZpY2F0aW9uLnRpdGxlfTwvaDQ+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gbXQtMVwiPntub3RpZmljYXRpb24ubWVzc2FnZX08L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcmVtb3ZlTm90aWZpY2F0aW9uKG5vdGlmaWNhdGlvbi5pZCl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1sLTMgdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktNjAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgw5dcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICkpfVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJ1c2VTZXNzaW9uIiwiTm90aWZpY2F0aW9uVG9hc3QiLCJkYXRhIiwic2Vzc2lvbiIsInN0YXR1cyIsIm5vdGlmaWNhdGlvbnMiLCJzZXROb3RpZmljYXRpb25zIiwicmVtb3ZlTm90aWZpY2F0aW9uIiwiaWQiLCJwcmV2IiwiZmlsdGVyIiwibiIsImFkZE5vdGlmaWNhdGlvbiIsIm5vdGlmaWNhdGlvbiIsIkRhdGUiLCJub3ciLCJNYXRoIiwicmFuZG9tIiwidG9TdHJpbmciLCJzdWJzdHJpbmciLCJuZXdOb3RpZmljYXRpb24iLCJzZXRUaW1lb3V0IiwidXNlciIsImNoZWNrRm9yTmV3Tm90aWZpY2F0aW9ucyIsInJlc3BvbnNlIiwiZmV0Y2giLCJoZWFkZXJzIiwib2siLCJqc29uIiwiY29uc29sZSIsIndhcm4iLCJzdGF0dXNUZXh0IiwiZXJyb3IiLCJUeXBlRXJyb3IiLCJtZXNzYWdlIiwiaW5jbHVkZXMiLCJ0aW1lb3V0SWQiLCJpbnRlcnZhbCIsInNldEludGVydmFsIiwiY2xlYXJUaW1lb3V0IiwiY2xlYXJJbnRlcnZhbCIsIndpbmRvdyIsInNob3dOb3RpZmljYXRpb25Ub2FzdCIsImdldFRvYXN0U3R5bGVzIiwidHlwZSIsImJhc2VTdHlsZXMiLCJnZXRJY29uIiwibGVuZ3RoIiwiZGl2IiwiY2xhc3NOYW1lIiwibWFwIiwic3BhbiIsImg0IiwidGl0bGUiLCJwIiwiYnV0dG9uIiwib25DbGljayJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/components/NotificationToast.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/Search.tsx":
/*!***********************************!*\
  !*** ./app/components/Search.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SearchBox: () => (/* binding */ SearchBox)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useSearch__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../hooks/useSearch */ \"(ssr)/./app/hooks/useSearch.ts\");\n/* harmony import */ var _barrel_optimize_names_IoIosSearch_react_icons_io__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=IoIosSearch!=!react-icons/io */ \"(ssr)/./node_modules/react-icons/io/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ SearchBox auto */ \n\n\n\n\nfunction SearchBox() {\n    const [localQuery, setLocalQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputFocused, setInputFocused] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { getSuggestions } = (0,_hooks_useSearch__WEBPACK_IMPORTED_MODULE_3__.useSearch)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SearchBox.useEffect\": ()=>{\n            if (!localQuery || !inputFocused) {\n                setSuggestions([]);\n                return;\n            }\n            const timer = setTimeout({\n                \"SearchBox.useEffect.timer\": ()=>{\n                    setSuggestions(getSuggestions(localQuery));\n                }\n            }[\"SearchBox.useEffect.timer\"], 200);\n            return ({\n                \"SearchBox.useEffect\": ()=>clearTimeout(timer)\n            })[\"SearchBox.useEffect\"];\n        }\n    }[\"SearchBox.useEffect\"], [\n        localQuery,\n        inputFocused,\n        getSuggestions\n    ]);\n    const handleSearch = ()=>{\n        if (localQuery.trim()) {\n            // Construct the URL manually for next/navigation router\n            router.push(`/search?q=${encodeURIComponent(localQuery)}`);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-3xl mx-auto \",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        value: localQuery,\n                        onChange: (e)=>setLocalQuery(e.target.value),\n                        onFocus: ()=>setInputFocused(true),\n                        onBlur: ()=>setTimeout(()=>setInputFocused(false), 200),\n                        onKeyDown: (e)=>e.key === \"Enter\" && handleSearch(),\n                        placeholder: \"Search Authors, Books and Journals...\",\n                        className: \"w-full px-6 py-4 rounded-lg text-base text-gray-800 bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 shadow-lg\",\n                        \"aria-label\": \"Search content\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\Search.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleSearch,\n                        className: \"absolute hidden sm:block right-2 top-2 px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 transition-colors\",\n                        disabled: !localQuery.trim(),\n                        children: \"Search\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\Search.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"block md:hidden absolute right-2 top-2 text-gray-400 py-2 text-3xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IoIosSearch_react_icons_io__WEBPACK_IMPORTED_MODULE_4__.IoIosSearch, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\Search.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\Search.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\Search.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            suggestions.length > 0 && inputFocused && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"absolute z-10 w-full mt-1 bg-white border rounded-lg shadow-lg max-h-60 overflow-auto\",\n                children: suggestions.map((suggestion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"p-3 hover:bg-gray-100 cursor-pointer\",\n                        onMouseDown: (e)=>e.preventDefault(),\n                        onClick: ()=>{\n                            setLocalQuery(suggestion.text);\n                            handleSearch();\n                        },\n                        children: [\n                            suggestion.text,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 text-sm ml-2\",\n                                children: [\n                                    \"(\",\n                                    suggestion.count,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\Search.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, suggestion.text, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\Search.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\Search.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\Search.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9TZWFyY2gudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUM0QztBQUNBO0FBQ0c7QUFFRjtBQUV0QyxTQUFTSztJQUNkLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHUCwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNRLGFBQWFDLGVBQWUsR0FBR1QsK0NBQVFBLENBQWUsRUFBRTtJQUMvRCxNQUFNLENBQUNVLGNBQWNDLGdCQUFnQixHQUFHWCwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNWSxTQUFTViwwREFBU0E7SUFDeEIsTUFBTSxFQUFFVyxjQUFjLEVBQUUsR0FBR1YsMkRBQVNBO0lBRXBDRixnREFBU0E7K0JBQUM7WUFDUixJQUFJLENBQUNLLGNBQWMsQ0FBQ0ksY0FBYztnQkFDaENELGVBQWUsRUFBRTtnQkFDakI7WUFDRjtZQUVBLE1BQU1LLFFBQVFDOzZDQUFXO29CQUN2Qk4sZUFBZUksZUFBZVA7Z0JBQ2hDOzRDQUFHO1lBRUg7dUNBQU8sSUFBTVUsYUFBYUY7O1FBQzVCOzhCQUFHO1FBQUNSO1FBQVlJO1FBQWNHO0tBQWU7SUFFN0MsTUFBTUksZUFBZTtRQUNuQixJQUFJWCxXQUFXWSxJQUFJLElBQUk7WUFDckIsd0RBQXdEO1lBQ3hETixPQUFPTyxJQUFJLENBQUMsQ0FBQyxVQUFVLEVBQUVDLG1CQUFtQmQsYUFBYTtRQUMzRDtJQUNGO0lBRUEscUJBQ0UsOERBQUNlO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUNDQyxNQUFLO3dCQUNMQyxPQUFPbkI7d0JBQ1BvQixVQUFVLENBQUNDLElBQU1wQixjQUFjb0IsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO3dCQUM3Q0ksU0FBUyxJQUFNbEIsZ0JBQWdCO3dCQUMvQm1CLFFBQVEsSUFBTWYsV0FBVyxJQUFNSixnQkFBZ0IsUUFBUTt3QkFDdkRvQixXQUFXLENBQUNKLElBQU1BLEVBQUVLLEdBQUcsS0FBSyxXQUFXZjt3QkFDdkNnQixhQUFZO3dCQUNaWCxXQUFVO3dCQUNWWSxjQUFXOzs7Ozs7a0NBRWIsOERBQUNDO3dCQUNDQyxTQUFTbkI7d0JBQ1RLLFdBQVU7d0JBQ1ZlLFVBQVUsQ0FBQy9CLFdBQVdZLElBQUk7a0NBQzNCOzs7Ozs7a0NBR0QsOERBQUNvQjt3QkFBS2hCLFdBQVU7a0NBQ2QsNEVBQUNsQiwwRkFBV0E7Ozs7Ozs7Ozs7Ozs7Ozs7WUFJZkksWUFBWStCLE1BQU0sR0FBRyxLQUFLN0IsOEJBQ3pCLDhEQUFDOEI7Z0JBQUdsQixXQUFVOzBCQUNYZCxZQUFZaUMsR0FBRyxDQUFDLENBQUNDLDJCQUNoQiw4REFBQ0M7d0JBRUNyQixXQUFVO3dCQUNWc0IsYUFBYSxDQUFDakIsSUFBTUEsRUFBRWtCLGNBQWM7d0JBQ3BDVCxTQUFTOzRCQUNQN0IsY0FBY21DLFdBQVdJLElBQUk7NEJBQzdCN0I7d0JBQ0Y7OzRCQUVDeUIsV0FBV0ksSUFBSTswQ0FDaEIsOERBQUNSO2dDQUFLaEIsV0FBVTs7b0NBQTZCO29DQUN6Q29CLFdBQVdLLEtBQUs7b0NBQUM7Ozs7Ozs7O3VCQVZoQkwsV0FBV0ksSUFBSTs7Ozs7Ozs7Ozs7Ozs7OztBQWtCbEMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmljaGVcXFRpbmEgRWR1Y2F0aW9uLm9yZ1xcUmV2aWV3IFJlcXVlc3QgYmFja2VuZFxcQVJSUy1OZXh0SlNcXHRpbmEtZWR1Y2F0aW9uXFxhcHBcXGNvbXBvbmVudHNcXFNlYXJjaC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xyXG5pbXBvcnQgeyB1c2VTZWFyY2ggfSBmcm9tICcuLi9ob29rcy91c2VTZWFyY2gnO1xyXG5pbXBvcnQgeyBTdWdnZXN0aW9uIH0gZnJvbSAnLi4vdHlwZXMvc2VhcmNoJztcclxuaW1wb3J0IHsgSW9Jb3NTZWFyY2ggfSBmcm9tICdyZWFjdC1pY29ucy9pbyc7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gU2VhcmNoQm94KCkge1xyXG4gIGNvbnN0IFtsb2NhbFF1ZXJ5LCBzZXRMb2NhbFF1ZXJ5XSA9IHVzZVN0YXRlKCcnKTtcclxuICBjb25zdCBbc3VnZ2VzdGlvbnMsIHNldFN1Z2dlc3Rpb25zXSA9IHVzZVN0YXRlPFN1Z2dlc3Rpb25bXT4oW10pO1xyXG4gIGNvbnN0IFtpbnB1dEZvY3VzZWQsIHNldElucHV0Rm9jdXNlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XHJcbiAgY29uc3QgeyBnZXRTdWdnZXN0aW9ucyB9ID0gdXNlU2VhcmNoKCk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoIWxvY2FsUXVlcnkgfHwgIWlucHV0Rm9jdXNlZCkge1xyXG4gICAgICBzZXRTdWdnZXN0aW9ucyhbXSk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuICAgIFxyXG4gICAgY29uc3QgdGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgc2V0U3VnZ2VzdGlvbnMoZ2V0U3VnZ2VzdGlvbnMobG9jYWxRdWVyeSkpO1xyXG4gICAgfSwgMjAwKTtcclxuICAgIFxyXG4gICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0aW1lcik7XHJcbiAgfSwgW2xvY2FsUXVlcnksIGlucHV0Rm9jdXNlZCwgZ2V0U3VnZ2VzdGlvbnNdKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlU2VhcmNoID0gKCkgPT4ge1xyXG4gICAgaWYgKGxvY2FsUXVlcnkudHJpbSgpKSB7XHJcbiAgICAgIC8vIENvbnN0cnVjdCB0aGUgVVJMIG1hbnVhbGx5IGZvciBuZXh0L25hdmlnYXRpb24gcm91dGVyXHJcbiAgICAgIHJvdXRlci5wdXNoKGAvc2VhcmNoP3E9JHtlbmNvZGVVUklDb21wb25lbnQobG9jYWxRdWVyeSl9YCk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctM3hsIG14LWF1dG8gXCI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cclxuICAgICAgICA8aW5wdXRcclxuICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgIHZhbHVlPXtsb2NhbFF1ZXJ5fVxyXG4gICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRMb2NhbFF1ZXJ5KGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgIG9uRm9jdXM9eygpID0+IHNldElucHV0Rm9jdXNlZCh0cnVlKX1cclxuICAgICAgICAgIG9uQmx1cj17KCkgPT4gc2V0VGltZW91dCgoKSA9PiBzZXRJbnB1dEZvY3VzZWQoZmFsc2UpLCAyMDApfVxyXG4gICAgICAgICAgb25LZXlEb3duPXsoZSkgPT4gZS5rZXkgPT09IFwiRW50ZXJcIiAmJiBoYW5kbGVTZWFyY2goKX1cclxuICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VhcmNoIEF1dGhvcnMsIEJvb2tzIGFuZCBKb3VybmFscy4uLlwiXHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNiBweS00IHJvdW5kZWQtbGcgdGV4dC1iYXNlIHRleHQtZ3JheS04MDAgYmctd2hpdGUgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtMzAwIHNoYWRvdy1sZ1wiXHJcbiAgICAgICAgICBhcmlhLWxhYmVsPVwiU2VhcmNoIGNvbnRlbnRcIlxyXG4gICAgICAgIC8+XHJcbiAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgb25DbGljaz17aGFuZGxlU2VhcmNofVxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaGlkZGVuIHNtOmJsb2NrIHJpZ2h0LTIgdG9wLTIgcHgtNCBweS0yIGJnLWJsdWUtOTAwIHRleHQtd2hpdGUgcm91bmRlZC1tZCBob3ZlcjpiZy1ibHVlLTgwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXHJcbiAgICAgICAgICBkaXNhYmxlZD17IWxvY2FsUXVlcnkudHJpbSgpfVxyXG4gICAgICAgID5cclxuICAgICAgICAgIFNlYXJjaFxyXG4gICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJsb2NrIG1kOmhpZGRlbiBhYnNvbHV0ZSByaWdodC0yIHRvcC0yIHRleHQtZ3JheS00MDAgcHktMiB0ZXh0LTN4bFwiPlxyXG4gICAgICAgICAgPElvSW9zU2VhcmNoIC8+XHJcbiAgICAgICAgPC9zcGFuPlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIHtzdWdnZXN0aW9ucy5sZW5ndGggPiAwICYmIGlucHV0Rm9jdXNlZCAmJiAoXHJcbiAgICAgICAgPHVsIGNsYXNzTmFtZT1cImFic29sdXRlIHotMTAgdy1mdWxsIG10LTEgYmctd2hpdGUgYm9yZGVyIHJvdW5kZWQtbGcgc2hhZG93LWxnIG1heC1oLTYwIG92ZXJmbG93LWF1dG9cIj5cclxuICAgICAgICAgIHtzdWdnZXN0aW9ucy5tYXAoKHN1Z2dlc3Rpb24pID0+IChcclxuICAgICAgICAgICAgPGxpXHJcbiAgICAgICAgICAgICAga2V5PXtzdWdnZXN0aW9uLnRleHR9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0zIGhvdmVyOmJnLWdyYXktMTAwIGN1cnNvci1wb2ludGVyXCJcclxuICAgICAgICAgICAgICBvbk1vdXNlRG93bj17KGUpID0+IGUucHJldmVudERlZmF1bHQoKX0gLy8gUHJldmVudCBpbW1lZGlhdGUgYmx1clxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgIHNldExvY2FsUXVlcnkoc3VnZ2VzdGlvbi50ZXh0KTtcclxuICAgICAgICAgICAgICAgIGhhbmRsZVNlYXJjaCgpO1xyXG4gICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICB7c3VnZ2VzdGlvbi50ZXh0fVxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgdGV4dC1zbSBtbC0yXCI+XHJcbiAgICAgICAgICAgICAgICAoe3N1Z2dlc3Rpb24uY291bnR9KVxyXG4gICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgPC9saT5cclxuICAgICAgICAgICkpfVxyXG4gICAgICAgIDwvdWw+XHJcbiAgICAgICl9XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59Il0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUm91dGVyIiwidXNlU2VhcmNoIiwiSW9Jb3NTZWFyY2giLCJTZWFyY2hCb3giLCJsb2NhbFF1ZXJ5Iiwic2V0TG9jYWxRdWVyeSIsInN1Z2dlc3Rpb25zIiwic2V0U3VnZ2VzdGlvbnMiLCJpbnB1dEZvY3VzZWQiLCJzZXRJbnB1dEZvY3VzZWQiLCJyb3V0ZXIiLCJnZXRTdWdnZXN0aW9ucyIsInRpbWVyIiwic2V0VGltZW91dCIsImNsZWFyVGltZW91dCIsImhhbmRsZVNlYXJjaCIsInRyaW0iLCJwdXNoIiwiZW5jb2RlVVJJQ29tcG9uZW50IiwiZGl2IiwiY2xhc3NOYW1lIiwiaW5wdXQiLCJ0eXBlIiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJvbkZvY3VzIiwib25CbHVyIiwib25LZXlEb3duIiwia2V5IiwicGxhY2Vob2xkZXIiLCJhcmlhLWxhYmVsIiwiYnV0dG9uIiwib25DbGljayIsImRpc2FibGVkIiwic3BhbiIsImxlbmd0aCIsInVsIiwibWFwIiwic3VnZ2VzdGlvbiIsImxpIiwib25Nb3VzZURvd24iLCJwcmV2ZW50RGVmYXVsdCIsInRleHQiLCJjb3VudCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/components/Search.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/UserDropdown.tsx":
/*!*****************************************!*\
  !*** ./app/components/UserDropdown.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UserDropdown)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_FaBookOpen_FaChevronDown_FaCog_FaSignOutAlt_FaTachometerAlt_FaUser_FaUserShield_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FaBookOpen,FaChevronDown,FaCog,FaSignOutAlt,FaTachometerAlt,FaUser,FaUserShield!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction UserDropdown({ user }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserDropdown.useEffect\": ()=>{\n            function handleClickOutside(event) {\n                if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                    setIsOpen(false);\n                }\n            }\n            document.addEventListener(\"mousedown\", handleClickOutside);\n            return ({\n                \"UserDropdown.useEffect\": ()=>{\n                    document.removeEventListener(\"mousedown\", handleClickOutside);\n                }\n            })[\"UserDropdown.useEffect\"];\n        }\n    }[\"UserDropdown.useEffect\"], []);\n    const handleSignOut = ()=>{\n        (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)({\n            callbackUrl: \"/\"\n        });\n    };\n    const getInitials = (name)=>{\n        if (!name) return \"U\";\n        return name.split(\" \").map((word)=>word.charAt(0)).join(\"\").toUpperCase().slice(0, 2);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center space-x-2 p-1 rounded-full hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n                \"aria-label\": \"User menu\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-9 h-9 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center overflow-hidden border-2 border-white shadow-sm\",\n                        children: user.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            src: user.image,\n                            alt: user.name || \"User\",\n                            width: 36,\n                            height: 36,\n                            className: \"w-full h-full object-cover\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium text-white\",\n                            children: getInitials(user.name)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBookOpen_FaChevronDown_FaCog_FaSignOutAlt_FaTachometerAlt_FaUser_FaUserShield_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaChevronDown, {\n                        className: `w-3 h-3 text-gray-500 transition-transform duration-200 hidden sm:block ${isOpen ? \"rotate-180\" : \"\"}`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-64 sm:w-72 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 max-w-[calc(100vw-2rem)] sm:max-w-none animate-in fade-in-0 zoom-in-95 duration-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden\",\n                                    children: user.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        src: user.image,\n                                        alt: user.name || \"User\",\n                                        width: 40,\n                                        height: 40,\n                                        className: \"w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-medium text-gray-600\",\n                                        children: getInitials(user.name)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                            children: user.name || \"User\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 truncate\",\n                                            children: user.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, this),\n                                        user.role && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mt-1\",\n                                            children: user.role.toLowerCase()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/dashboard\",\n                                className: \"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\",\n                                onClick: ()=>setIsOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBookOpen_FaChevronDown_FaCog_FaSignOutAlt_FaTachometerAlt_FaUser_FaUserShield_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTachometerAlt, {\n                                        className: \"w-4 h-4 mr-3 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Dashboard\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/profile\",\n                                className: \"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\",\n                                onClick: ()=>setIsOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBookOpen_FaChevronDown_FaCog_FaSignOutAlt_FaTachometerAlt_FaUser_FaUserShield_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaUser, {\n                                        className: \"w-4 h-4 mr-3 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Profile\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this),\n                            user.role === \"REVIEWER\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/dashboard/reviews\",\n                                className: \"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\",\n                                onClick: ()=>setIsOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBookOpen_FaChevronDown_FaCog_FaSignOutAlt_FaTachometerAlt_FaUser_FaUserShield_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaBookOpen, {\n                                        className: \"w-4 h-4 mr-3 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"My Reviews\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 15\n                            }, this),\n                            user.role === \"ADMIN\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/dashboard/admin\",\n                                className: \"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\",\n                                onClick: ()=>setIsOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBookOpen_FaChevronDown_FaCog_FaSignOutAlt_FaTachometerAlt_FaUser_FaUserShield_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaUserShield, {\n                                        className: \"w-4 h-4 mr-3 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Admin Panel\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/dashboard/settings\",\n                                className: \"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\",\n                                onClick: ()=>setIsOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBookOpen_FaChevronDown_FaCog_FaSignOutAlt_FaTachometerAlt_FaUser_FaUserShield_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaCog, {\n                                        className: \"w-4 h-4 mr-3 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-gray-200 my-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleSignOut,\n                        className: \"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBookOpen_FaChevronDown_FaCog_FaSignOutAlt_FaTachometerAlt_FaUser_FaUserShield_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaSignOutAlt, {\n                                className: \"w-4 h-4 mr-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this),\n                            \"Sign Out\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n                lineNumber: 93,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\UserDropdown.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/UserDropdown.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/authSect.tsx":
/*!*************************************!*\
  !*** ./app/components/authSect.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthSect)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _NotificationBell__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./NotificationBell */ \"(ssr)/./app/components/NotificationBell.tsx\");\n/* harmony import */ var _UserDropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UserDropdown */ \"(ssr)/./app/components/UserDropdown.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction AuthSect() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const getCurrentUrl = ()=>{\n        const params = searchParams.toString();\n        return params ? `${pathname}?${params}` : pathname;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-center items-center gap-5 relative text-gray-400\",\n        children: [\n            !session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"px-5 py-2 border border-black rounded text-gray-800 hover:bg-black hover:text-white hover:bg-opacity-10 transition-colors\",\n                    onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.signIn)(undefined, {\n                            callbackUrl: getCurrentUrl()\n                        }),\n                    children: \"Sign In\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\authSect.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false),\n            session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationBell__WEBPACK_IMPORTED_MODULE_3__.NotificationBell, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\authSect.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserDropdown__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        user: session.user\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\authSect.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\authSect.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/authSect.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/home/<USER>":
/*!*****************************************************!*\
  !*** ./app/components/home/<USER>
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PublicationCarousel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// Removed unused function getPublicationTypeColor\nfunction getPublicationTypeIcon(type) {\n    switch(type){\n        case \"BOOK\":\n            return \"📚\";\n        case \"EBOOK\":\n            return \"📱\";\n        case \"AUDIOBOOK\":\n            return \"🎧\";\n        case \"JOURNAL\":\n            return \"📄\";\n        case \"ARTICLE\":\n            return \"📝\";\n        default:\n            return \"📄\";\n    }\n}\nfunction getGradientColor(type) {\n    switch(type){\n        case \"books\":\n            return \"from-blue-500 to-blue-700\";\n        case \"journals\":\n            return \"from-blue-600 to-blue-800\";\n        case \"articles\":\n            return \"from-gray-600 to-gray-800\";\n        default:\n            return \"from-gray-500 to-gray-700\";\n    }\n}\nfunction PublicationCarousel({ title, type, publications }) {\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(3); // Start at 3 to show first real item (after 3 clones)\n    const [isAutoPlaying, setIsAutoPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isTransitioning, setIsTransitioning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pendingJump, setPendingJump] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const carouselRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Show up to 6 publications at a time\n    const visiblePublications = publications.slice(0, 6);\n    const totalSlides = visiblePublications.length;\n    const nextSlide = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PublicationCarousel.useCallback[nextSlide]\": ()=>{\n            if (totalSlides <= 1) return;\n            setCurrentIndex({\n                \"PublicationCarousel.useCallback[nextSlide]\": (prevIndex)=>prevIndex + 1\n            }[\"PublicationCarousel.useCallback[nextSlide]\"]);\n        }\n    }[\"PublicationCarousel.useCallback[nextSlide]\"], [\n        totalSlides\n    ]);\n    // Auto-scroll functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PublicationCarousel.useEffect\": ()=>{\n            if (!isAutoPlaying || totalSlides === 0) return;\n            const interval = setInterval({\n                \"PublicationCarousel.useEffect.interval\": ()=>{\n                    nextSlide();\n                }\n            }[\"PublicationCarousel.useEffect.interval\"], 4000); // Change slide every 4 seconds\n            return ({\n                \"PublicationCarousel.useEffect\": ()=>clearInterval(interval)\n            })[\"PublicationCarousel.useEffect\"];\n        }\n    }[\"PublicationCarousel.useEffect\"], [\n        isAutoPlaying,\n        totalSlides,\n        nextSlide\n    ]);\n    const prevSlide = ()=>{\n        if (totalSlides <= 1) return;\n        setCurrentIndex((prevIndex)=>prevIndex - 1);\n    };\n    const goToSlide = (index)=>{\n        setCurrentIndex(index + 3); // Add 3 because we start at index 3 (after clones)\n    };\n    // Handle infinite scroll transitions using onTransitionEnd\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PublicationCarousel.useEffect\": ()=>{\n            if (totalSlides <= 1) return;\n            if (currentIndex <= 2) {\n                setPendingJump(totalSlides + currentIndex);\n            } else if (currentIndex >= totalSlides + 3) {\n                setPendingJump(currentIndex - totalSlides);\n            } else {\n                setPendingJump(null);\n            }\n        }\n    }[\"PublicationCarousel.useEffect\"], [\n        currentIndex,\n        totalSlides\n    ]);\n    const handleTransitionEnd = ()=>{\n        if (pendingJump !== null) {\n            setIsTransitioning(false);\n            setCurrentIndex(pendingJump);\n            setPendingJump(null);\n            // Re-enable transition after jump\n            setTimeout(()=>setIsTransitioning(true), 50);\n        }\n    };\n    if (publications.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-6\",\n                    children: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12 bg-gray-50 rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"No publications available\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, this);\n    }\n    const gradientClass = getGradientColor(type);\n    // Create extended array for infinite scroll - add enough clones for seamless circular view\n    const extendedPublications = totalSlides > 0 ? [\n        // Add clones at the beginning (last 3 items)\n        visiblePublications[totalSlides - 3] || visiblePublications[0],\n        visiblePublications[totalSlides - 2] || visiblePublications[0],\n        visiblePublications[totalSlides - 1],\n        // Original items\n        ...visiblePublications,\n        // Add clones at the end (first 3 items)\n        visiblePublications[0],\n        visiblePublications[1] || visiblePublications[0],\n        visiblePublications[2] || visiblePublications[0]\n    ] : [];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-16\",\n        onMouseEnter: ()=>setIsAutoPlaying(false),\n        onMouseLeave: ()=>setIsAutoPlaying(true),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: `/${type}`,\n                        className: \"text-blue-900 hover:text-blue-700 font-medium text-sm\",\n                        children: \"View All →\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-hidden rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: carouselRef,\n                        className: `flex ${isTransitioning ? \"transition-transform duration-500 ease-in-out\" : \"\"}`,\n                        style: {\n                            transform: `translateX(-${currentIndex * (100 / 3)}%)`\n                        },\n                        onTransitionEnd: handleTransitionEnd,\n                        children: extendedPublications.map((publication, index)=>{\n                            // Determine if this is the active (center) card\n                            const isActive = index === currentIndex;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-1/3 flex-shrink-0 px-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `flex bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200 h-72 ${isActive ? \"scale-105 z-10 shadow-lg\" : \"scale-95 opacity-75\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `w-48 h-full bg-gradient-to-br ${gradientClass} relative flex-shrink-0`,\n                                            children: publication.cover ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: publication.cover,\n                                                alt: publication.title,\n                                                fill: true,\n                                                className: \"object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center h-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-4xl text-white opacity-80\",\n                                                    children: getPublicationTypeIcon(publication.type)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 p-4 flex flex-col justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                                            className: \"text-sm text-gray-700 italic mb-3 leading-relaxed line-clamp-3\",\n                                                            children: [\n                                                                '\"',\n                                                                publication.abstract ? publication.abstract.substring(0, 100) + \"...\" : \"Gives us hope, humour and guidance at a time when all are in short supply\",\n                                                                '\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm mb-3\",\n                                                            children: publication.user.name || \"Timothy Snyder\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-bold text-gray-900 mb-2 line-clamp-2\",\n                                                            children: publication.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm mb-4\",\n                                                            children: publication.user.name || \"Rutger Bregman\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: `/repository/${publication.id}`,\n                                                        className: \"inline-block px-4 py-2 bg-gray-600 text-white font-medium text-sm rounded hover:bg-gray-700 transition-colors\",\n                                                        children: \"READ NOW\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 17\n                                }, this)\n                            }, `${publication.id}-${index}`, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this),\n                    visiblePublications.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: prevSlide,\n                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 shadow-lg transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-gray-600\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M15 19l-7-7 7-7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: nextSlide,\n                                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 shadow-lg transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-gray-600\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M9 5l7 7-7 7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            visiblePublications.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center mt-4 space-x-2\",\n                children: visiblePublications.map((_, index)=>{\n                    // Calculate which real item is currently active (subtract 3 for the clones at beginning)\n                    const activeRealIndex = (currentIndex - 3 + totalSlides) % totalSlides;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>goToSlide(index),\n                        className: `w-3 h-3 rounded-full transition-all duration-200 ${index === activeRealIndex ? \"bg-gray-900\" : \"bg-gray-600 bg-opacity-50\"}`\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 15\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n                lineNumber: 301,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home\\\\PublicationCarousel.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./app/components/home_nav.tsx":
/*!*************************************!*\
  !*** ./app/components/home_nav.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomeNav)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _authSect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./authSect */ \"(ssr)/./app/components/authSect.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_FaBars_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FaBars,FaTimes!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction HomeNav() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname)();\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n    };\n    // Check if user is signed in and on home page\n    const isSignedInOnHomePage = session && pathname === \"/\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed top-0 w-full bg-white shadow-md z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto w-[90%] max-w-7xl\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/\",\n                        className: \"text-2xl font-bold text-gray-800\",\n                        children: \"Tina Education\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"text-gray-800 text-2xl md:hidden\",\n                        onClick: toggleMenu,\n                        \"aria-label\": \"Toggle Menu\",\n                        children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaTimes, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 27\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaBars, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 41\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: `${isMenuOpen ? \"block\" : \"hidden\"} md:block absolute md:static top-full left-0 w-full md:w-auto bg-white md:bg-transparent shadow-md md:shadow-none`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"flex flex-col md:flex-row items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"ml-0 md:ml-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: isSignedInOnHomePage ? \"/dashboard\" : \"/\",\n                                        className: \"text-gray-800 font-medium\",\n                                        children: isSignedInOnHomePage ? \"Dashboard\" : \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"ml-0 md:ml-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/books\",\n                                        className: \"text-gray-800 font-medium\",\n                                        children: \"Books\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"ml-0 md:ml-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/journals\",\n                                        className: \"text-gray-800 font-medium\",\n                                        children: \"Journals\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, this),\n                                session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"ml-0 md:ml-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/wishlist\",\n                                        className: \"text-gray-800 font-medium\",\n                                        children: \"Wishlist\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 17\n                                }, this),\n                                session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"ml-0 md:ml-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/cart\",\n                                        className: \"text-gray-800 font-medium\",\n                                        children: \"Cart\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"ml-0 md:ml-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"#\",\n                                        className: \"text-gray-800 font-medium\",\n                                        children: \"Publisher with Us\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"ml-0 md:ml-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center items-center gap-5 mb-8 md:mb-0 relative text-gray-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                                            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Loading...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 39\n                                            }, void 0),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_authSect__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/home_nav.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/AuthProvider.tsx":
/*!**************************************!*\
  !*** ./app/context/AuthProvider.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AuthProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\context\\\\AuthProvider.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29udGV4dC9BdXRoUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRWtEO0FBRW5DLFNBQVNDLGFBQWEsRUFBRUMsUUFBUSxFQUFpQztJQUM5RSxxQkFDRSw4REFBQ0YsNERBQWVBO2tCQUNiRTs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmljaGVcXFRpbmEgRWR1Y2F0aW9uLm9yZ1xcUmV2aWV3IFJlcXVlc3QgYmFja2VuZFxcQVJSUy1OZXh0SlNcXHRpbmEtZWR1Y2F0aW9uXFxhcHBcXGNvbnRleHRcXEF1dGhQcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0JztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEF1dGhQcm92aWRlcih7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxTZXNzaW9uUHJvdmlkZXI+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvU2Vzc2lvblByb3ZpZGVyPlxyXG4gICk7XHJcbn0iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiQXV0aFByb3ZpZGVyIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/context/AuthProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./app/hooks/useSearch.ts":
/*!********************************!*\
  !*** ./app/hooks/useSearch.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSearch: () => (/* binding */ useSearch)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _services_search_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/search-engine */ \"(ssr)/./app/services/search-engine.ts\");\n/* harmony import */ var _useSearchIndex__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useSearchIndex */ \"(ssr)/./app/hooks/useSearchIndex.ts\");\n// hooks/useSearch.ts\n\n\n\nfunction useSearch() {\n    const { index, loading, error } = (0,_useSearchIndex__WEBPACK_IMPORTED_MODULE_2__.useSearchIndex)();\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isSearching, setIsSearching] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Initialize search engine when index loads\n    const searchEngine = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useSearch.useMemo[searchEngine]\": ()=>{\n            return index ? new _services_search_engine__WEBPACK_IMPORTED_MODULE_1__.SearchEngine(index) : null;\n        }\n    }[\"useSearch.useMemo[searchEngine]\"], [\n        index\n    ]);\n    // Perform search when query or filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSearch.useEffect\": ()=>{\n            if (!searchEngine || !query.trim()) {\n                setResults(null);\n                return;\n            }\n            setIsSearching(true);\n            const timer = setTimeout({\n                \"useSearch.useEffect.timer\": ()=>{\n                    const searchResults = searchEngine.search(query, {\n                        filters,\n                        boostRecent: true\n                    });\n                    setResults(searchResults);\n                    setIsSearching(false);\n                }\n            }[\"useSearch.useEffect.timer\"], 300); // Debounce\n            return ({\n                \"useSearch.useEffect\": ()=>clearTimeout(timer)\n            })[\"useSearch.useEffect\"];\n        }\n    }[\"useSearch.useEffect\"], [\n        query,\n        filters,\n        searchEngine\n    ]);\n    // Get autocomplete suggestions\n    const getSuggestions = (text)=>{\n        return searchEngine?.getSuggestions(text) || [];\n    };\n    return {\n        query,\n        setQuery,\n        filters,\n        setFilters,\n        results,\n        isSearching,\n        loading,\n        error,\n        getSuggestions\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/hooks/useSearch.ts\n");

/***/ }),

/***/ "(ssr)/./app/hooks/useSearchIndex.ts":
/*!*************************************!*\
  !*** ./app/hooks/useSearchIndex.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSearchIndex: () => (/* binding */ useSearchIndex)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useSearchIndex() {\n    const [index, setIndex] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSearchIndex.useEffect\": ()=>{\n            const loadIndex = {\n                \"useSearchIndex.useEffect.loadIndex\": async ()=>{\n                    try {\n                        const response = await fetch(\"/search-index.json\");\n                        const data = await response.json();\n                        // Convert arrays back to Sets (no 'this' needed)\n                        const processedIndex = {\n                            documents: data.documents,\n                            tokenIndex: deserializeIndex(data.tokenIndex),\n                            ngramIndex: deserializeIndex(data.ngramIndex),\n                            metadataIndex: {\n                                types: new Set(data.metadataIndex.types),\n                                authors: new Set(data.metadataIndex.authors),\n                                years: new Set(data.metadataIndex.years)\n                            }\n                        };\n                        setIndex(processedIndex);\n                    } catch (err) {\n                        setError(err instanceof Error ? err : new Error(\"Failed to load search index\"));\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"useSearchIndex.useEffect.loadIndex\"];\n            loadIndex();\n        }\n    }[\"useSearchIndex.useEffect\"], []);\n    return {\n        index,\n        loading,\n        error\n    };\n}\n// Standalone function (no 'this' needed)\nfunction deserializeIndex(index) {\n    return Object.fromEntries(Object.entries(index).map(([key, value])=>[\n            key,\n            new Set(value)\n        ]));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/hooks/useSearchIndex.ts\n");

/***/ }),

/***/ "(ssr)/./app/services/search-engine.ts":
/*!***************************************!*\
  !*** ./app/services/search-engine.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SearchEngine: () => (/* binding */ SearchEngine)\n/* harmony export */ });\nclass SearchEngine {\n    constructor(index){\n        this.index = index;\n    }\n    search(query, options = {}) {\n        // Normalize and tokenize the query\n        const queryTokens = this.tokenizeQuery(query);\n        // Get matching document IDs\n        let resultIds = this.getMatchingDocuments(queryTokens);\n        // Apply filters if specified\n        if (options.filters) {\n            resultIds = this.applyFilters(resultIds, options.filters);\n        }\n        // Convert IDs to documents and calculate scores\n        const results = this.scoreDocuments(Array.from(resultIds).map((id)=>this.index.documents[id]), queryTokens, options);\n        // Sort by score and apply pagination\n        return this.paginateResults(results, options);\n    }\n    tokenizeQuery(query) {\n        // Use the same tokenization as during indexing\n        const normalized = query.toLowerCase().replace(/[^\\w\\s-]/g, \"\").replace(/\\s+/g, \" \").trim();\n        const tokens = normalized.split(\" \");\n        return tokens.map((token)=>{\n            // Simple stemmer for the query (could use the same stemmer as indexing)\n            if (token.endsWith(\"s\")) return token.slice(0, -1);\n            if (token.endsWith(\"ing\")) return token.slice(0, -3);\n            return token;\n        });\n    }\n    getMatchingDocuments(queryTokens) {\n        if (queryTokens.length === 0) return new Set();\n        // Start with documents matching the first token\n        let results = new Set();\n        for (const token of queryTokens){\n            const tokenMatches = this.index.tokenIndex[token] || new Set();\n            const ngramMatches = this.getNgramMatches(token);\n            // Combine matches for this token\n            const combinedMatches = new Set([\n                ...tokenMatches,\n                ...ngramMatches\n            ]);\n            // For the first token, initialize results\n            if (results.size === 0) {\n                results = new Set(combinedMatches);\n            } else {\n                // For subsequent tokens, intersect with existing results\n                results = new Set([\n                    ...results\n                ].filter((id)=>combinedMatches.has(id)));\n            }\n            // Early exit if no matches\n            if (results.size === 0) break;\n        }\n        return results;\n    }\n    getNgramMatches(token) {\n        // Find ngrams that contain the token (for partial matching)\n        const matchingNgrams = Object.keys(this.index.ngramIndex).filter((ngram)=>ngram.includes(token));\n        return matchingNgrams.reduce((acc, ngram)=>{\n            const docs = this.index.ngramIndex[ngram] || new Set();\n            return new Set([\n                ...acc,\n                ...docs\n            ]);\n        }, new Set());\n    }\n    applyFilters(ids, filters) {\n        return new Set([\n            ...ids\n        ].filter((id)=>{\n            const doc = this.index.documents[id];\n            // Type filter\n            if (filters.types && filters.types.length > 0 && !filters.types.includes(doc.type)) {\n                return false;\n            }\n            // Author filter\n            if (filters.authors && filters.authors.length > 0 && !doc.authors.some((author)=>filters.authors.includes(author))) {\n                return false;\n            }\n            // Year range filter\n            if (filters.yearRange) {\n                const year = new Date(doc.publicationDate).getFullYear();\n                if (filters.yearRange.min && year < filters.yearRange.min || filters.yearRange.max && year > filters.yearRange.max) {\n                    return false;\n                }\n            }\n            return true;\n        }));\n    }\n    scoreDocuments(docs, queryTokens, options) {\n        return docs.map((doc)=>{\n            let score = 0;\n            // Term frequency scoring\n            const content = doc.searchableText.toLowerCase();\n            for (const token of queryTokens){\n                const regex = new RegExp(`\\\\b${token}\\\\b`, \"g\");\n                const matches = content.match(regex);\n                const frequency = matches ? matches.length : 0;\n                score += frequency * 2; // Higher weight for exact matches\n                // Partial matches\n                if (content.includes(token)) {\n                    score += 1;\n                }\n            }\n            // Field-specific boosts\n            if (doc.title.toLowerCase().includes(queryTokens.join(\" \"))) {\n                score += 10; // Big boost for title matches\n            }\n            // Apply document boost (from preprocessing)\n            score *= doc.boost;\n            // Freshness boost (optional)\n            if (options.boostRecent) {\n                const ageInYears = (Date.now() - new Date(doc.publicationDate).getTime()) / (1000 * 60 * 60 * 24 * 365);\n                score *= Math.max(0.5, 2 - ageInYears * 0.1);\n            }\n            return {\n                document: doc,\n                score\n            };\n        });\n    }\n    paginateResults(results, options) {\n        // Sort by score (descending)\n        const sorted = results.sort((a, b)=>b.score - a.score);\n        // Apply pagination\n        const page = options.page || 1;\n        const perPage = options.perPage || 10;\n        const start = (page - 1) * perPage;\n        const end = start + perPage;\n        return {\n            results: sorted.slice(start, end),\n            total: sorted.length,\n            page,\n            perPage,\n            totalPages: Math.ceil(sorted.length / perPage)\n        };\n    }\n    // Helper method for autocomplete\n    getSuggestions(query, limit = 5) {\n        if (!query) return [];\n        const normalized = query.toLowerCase();\n        const suggestions = new Map();\n        // Find matching tokens\n        for (const token of Object.keys(this.index.tokenIndex)){\n            if (token.startsWith(normalized)) {\n                const docs = this.index.tokenIndex[token];\n                suggestions.set(token, docs.size);\n            }\n        }\n        // Find matching ngrams\n        for (const ngram of Object.keys(this.index.ngramIndex)){\n            if (ngram.startsWith(normalized)) {\n                const docs = this.index.ngramIndex[ngram];\n                const currentCount = suggestions.get(ngram) || 0;\n                suggestions.set(ngram, currentCount + docs.size);\n            }\n        }\n        // Convert to array and sort by relevance\n        return Array.from(suggestions.entries()).sort((a, b)=>b[1] - a[1]).slice(0, limit).map(([text, count])=>({\n                text,\n                count\n            }));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/services/search-engine.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome%5C%5CPublicationCarousel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CSearch.tsx%22%2C%22ids%22%3A%5B%22SearchBox%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome%5C%5CPublicationCarousel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CSearch.tsx%22%2C%22ids%22%3A%5B%22SearchBox%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/home/<USER>/ \"(ssr)/./app/components/home/<USER>"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/Search.tsx */ \"(ssr)/./app/components/Search.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome%5C%5CPublicationCarousel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CSearch.tsx%22%2C%22ids%22%3A%5B%22SearchBox%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome_nav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CNotificationToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccontext%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome_nav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CNotificationToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccontext%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/home_nav.tsx */ \"(ssr)/./app/components/home_nav.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/NotificationToast.tsx */ \"(ssr)/./app/components/NotificationToast.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/AuthProvider.tsx */ \"(ssr)/./app/context/AuthProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome_nav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CNotificationToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccontext%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/react-icons","vendor-chunks/next-auth","vendor-chunks/@auth"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();