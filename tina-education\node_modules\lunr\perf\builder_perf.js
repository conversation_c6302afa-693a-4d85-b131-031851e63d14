suite('lunr.Builder', function () {
  var documents = [{
    id: 'a',
    title: 'Mr. <PERSON> kills Colonel <PERSON><PERSON>',
    body: 'Mr. <PERSON> killed Colonel <PERSON><PERSON> in the study with the candlestick. Mr<PERSON> is not a very nice fellow.',
    wordCount: 19
  },{
    id: 'b',
    title: 'Plumb waters plant',
    body: 'Professor <PERSON><PERSON><PERSON> has a green plant in his study',
    wordCount: 9
  },{
    id: 'c',
    title: '<PERSON> helps Professor',
    body: '<PERSON> watered Professor <PERSON><PERSON><PERSON> green plant while he was away from his office last week.',
    wordCount: 16
  }]

  this.add('build', function () {
    lunr(function () {
      this.ref('id')
      this.field('title')
      this.field('body')

      documents.forEach(function (doc) {
        this.add(doc)
      }, this)
    })
  })
})
