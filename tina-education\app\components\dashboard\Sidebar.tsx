"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState, useEffect } from "react";
import {
  FaBars,
  FaTimes,
  FaChartBar,
  FaBook,
  FaFileAlt,
  FaClipboardList,
  FaChartLine,
  FaUsers,
  FaComments,
  FaBell,
  FaBookOpen,
  FaNewspaper,
  FaFile,
  FaBookmark,
  FaTools,
  FaCalendarAlt,
  FaCog,
} from "react-icons/fa";

interface SidebarItemProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  onClick?: () => void;
}

function SidebarItem({ href, icon, label, onClick }: SidebarItemProps) {
  const pathname = usePathname();
  const isActive =
    pathname === href || (href !== "/dashboard" && pathname.startsWith(href));

  return (
    <li className="mb-1">
      <Link
        href={href}
        onClick={onClick}
        className={`flex items-center px-5 py-3 text-gray-800 font-medium hover:bg-blue-50 hover:text-blue-900 hover:border-l-3 hover:border-blue-900 transition-colors ${
          isActive ? "bg-blue-50 text-blue-900 border-l-3 border-blue-900" : ""
        }`}
      >
        <span className="mr-3 text-lg">{icon}</span>
        <span>{label}</span>
      </Link>
    </li>
  );
}

export default function Sidebar() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const sidebar = document.getElementById("mobile-sidebar");
      const toggleButton = document.getElementById("mobile-sidebar-toggle");

      if (
        sidebar &&
        !sidebar.contains(event.target as Node) &&
        toggleButton &&
        !toggleButton.contains(event.target as Node)
      ) {
        setIsMobileMenuOpen(false);
      }
    };

    if (isMobileMenuOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isMobileMenuOpen]);

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <>
      {/* Mobile Menu Toggle Button */}
      <button
        id="mobile-sidebar-toggle"
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        className="lg:hidden fixed top-24 left-4 z-50 p-2 bg-white rounded-md shadow-md border border-gray-200"
        aria-label="Toggle sidebar menu"
      >
        {isMobileMenuOpen ? (
          <FaTimes className="text-gray-600" />
        ) : (
          <FaBars className="text-gray-600" />
        )}
      </button>

      {/* Mobile Overlay */}
      {isMobileMenuOpen && (
        <div className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40 top-20" />
      )}

      {/* Sidebar */}
      <aside
        id="mobile-sidebar"
        className={`
          fixed lg:sticky w-64 bg-white shadow-md top-20 h-[calc(100vh-5rem)] overflow-y-auto border-r border-gray-200 flex-shrink-0 z-40
          transition-transform duration-300 ease-in-out
          ${isMobileMenuOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"}
        `}
      >
        <div className="py-5">
          <ul>
            <SidebarItem
              href="/dashboard"
              icon={<FaChartBar />}
              label="Dashboard"
              onClick={closeMobileMenu}
            />
            <SidebarItem
              href="/dashboard/publications"
              icon={<FaBook />}
              label="My Publications"
              onClick={closeMobileMenu}
            />
            <SidebarItem
              href="/dashboard/manuscripts"
              icon={<FaFileAlt />}
              label="Manuscripts"
              onClick={closeMobileMenu}
            />
            <SidebarItem
              href="/dashboard/reviews"
              icon={<FaClipboardList />}
              label="Reviews"
              onClick={closeMobileMenu}
            />
            <SidebarItem
              href="/dashboard/analytics"
              icon={<FaChartLine />}
              label="Analytics"
              onClick={closeMobileMenu}
            />
          </ul>

          <div className="mt-5 pt-5 border-t border-gray-200">
            <h3 className="px-5 mb-4 text-gray-400 text-sm uppercase">
              Collaboration
            </h3>
            <ul>
              <SidebarItem
                href="/dashboard/collaborators"
                icon={<FaUsers />}
                label="Co-authors"
                onClick={closeMobileMenu}
              />
              <SidebarItem
                href="/dashboard/messages"
                icon={<FaComments />}
                label="Messages"
                onClick={closeMobileMenu}
              />
              <SidebarItem
                href="/dashboard/notifications"
                icon={<FaBell />}
                label="Notifications"
                onClick={closeMobileMenu}
              />
            </ul>
          </div>

          <div className="mt-5 pt-5 border-t border-gray-200">
            <h3 className="px-5 mb-4 text-gray-400 text-sm uppercase">
              Repository
            </h3>
            <ul>
              <SidebarItem
                href="/repository"
                icon={<FaBook />}
                label="All Publications"
                onClick={closeMobileMenu}
              />
              <SidebarItem
                href="/books"
                icon={<FaBookOpen />}
                label="Books"
                onClick={closeMobileMenu}
              />
              <SidebarItem
                href="/journals"
                icon={<FaNewspaper />}
                label="Journals"
                onClick={closeMobileMenu}
              />
              <SidebarItem
                href="/articles"
                icon={<FaFile />}
                label="Articles"
                onClick={closeMobileMenu}
              />
            </ul>
          </div>

          <div className="mt-5 pt-5 border-t border-gray-200">
            <h3 className="px-5 mb-4 text-gray-400 text-sm uppercase">
              Resources
            </h3>
            <ul>
              <SidebarItem
                href="/dashboard/library"
                icon={<FaBookmark />}
                label="My Library"
                onClick={closeMobileMenu}
              />
              <SidebarItem
                href="/dashboard/tools"
                icon={<FaTools />}
                label="Writing Tools"
                onClick={closeMobileMenu}
              />
              <SidebarItem
                href="/dashboard/calendar"
                icon={<FaCalendarAlt />}
                label="Calendar"
                onClick={closeMobileMenu}
              />
              <SidebarItem
                href="/dashboard/settings"
                icon={<FaCog />}
                label="Settings"
                onClick={closeMobileMenu}
              />
            </ul>
          </div>
        </div>
      </aside>
    </>
  );
}
